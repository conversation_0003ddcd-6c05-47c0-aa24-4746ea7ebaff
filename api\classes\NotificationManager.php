<?php
/**
 * فئة إدارة الإشعارات
 * Notification Management Class
 */

class NotificationManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * إنشاء إشعار جديد
     * Create new notification
     */
    public function createNotification($user_id, $title, $message, $type = 'info') {
        try {
            $sql = "INSERT INTO notifications (user_id, title, message, type) 
                    VALUES (?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$user_id, $title, $message, $type]);
            
            if ($result) {
                return $this->db->lastInsertId();
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Create notification error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إشعارات المستخدم
     * Get user notifications
     */
    public function getUserNotifications($user_id, $limit = 50, $unread_only = false) {
        try {
            $sql = "SELECT * FROM notifications WHERE user_id = ?";
            $params = [$user_id];
            
            if ($unread_only) {
                $sql .= " AND is_read = 0";
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get user notifications error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تمييز الإشعارات كمقروءة
     * Mark notifications as read
     */
    public function markAsRead($notification_ids, $user_id) {
        try {
            if (empty($notification_ids)) {
                return false;
            }
            
            $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
            $sql = "UPDATE notifications 
                    SET is_read = 1 
                    WHERE id IN ({$placeholders}) AND user_id = ?";
            
            $params = array_merge($notification_ids, [$user_id]);
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
            
        } catch (Exception $e) {
            error_log('Mark notifications as read error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تمييز جميع الإشعارات كمقروءة
     * Mark all notifications as read
     */
    public function markAllAsRead($user_id) {
        try {
            $sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id]);
            
        } catch (Exception $e) {
            error_log('Mark all as read error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف إشعار
     * Delete notification
     */
    public function deleteNotification($notification_id, $user_id) {
        try {
            $sql = "DELETE FROM notifications WHERE id = ? AND user_id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$notification_id, $user_id]);
            
        } catch (Exception $e) {
            error_log('Delete notification error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف جميع الإشعارات المقروءة
     * Delete all read notifications
     */
    public function deleteReadNotifications($user_id) {
        try {
            $sql = "DELETE FROM notifications WHERE user_id = ? AND is_read = 1";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id]);
            
        } catch (Exception $e) {
            error_log('Delete read notifications error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على عدد الإشعارات غير المقروءة
     * Get unread notifications count
     */
    public function getUnreadCount($user_id) {
        try {
            $sql = "SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log('Get unread count error: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * إنشاء إشعارات تلقائية للتلقيحات المستحقة
     * Create automatic notifications for due vaccinations
     */
    public function createVaccinationReminders() {
        try {
            // البحث عن التلقيحات المستحقة خلال الأسبوع القادم
            $sql = "SELECT cv.*, c.name as child_name, c.nurse_id, v.name_ar as vaccine_name
                    FROM child_vaccinations cv
                    JOIN children c ON cv.child_id = c.id
                    JOIN vaccines v ON cv.vaccine_id = v.id
                    WHERE cv.is_completed = 0 
                    AND cv.due_date BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY)
                    AND c.nurse_id IS NOT NULL";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $due_vaccinations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($due_vaccinations as $vaccination) {
                // التحقق من عدم وجود إشعار مماثل
                $check_sql = "SELECT COUNT(*) FROM notifications 
                             WHERE user_id = ? 
                             AND title LIKE ? 
                             AND created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)";
                
                $check_stmt = $this->db->prepare($check_sql);
                $check_stmt->execute([
                    $vaccination['nurse_id'],
                    "%{$vaccination['child_name']}%{$vaccination['vaccine_name']}%"
                ]);
                
                if ($check_stmt->fetchColumn() == 0) {
                    $title = "تذكير: تلقيح مستحق";
                    $message = "الطفل {$vaccination['child_name']} يحتاج لقاح {$vaccination['vaccine_name']} في تاريخ {$vaccination['due_date']}";
                    
                    $this->createNotification($vaccination['nurse_id'], $title, $message, 'warning');
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('Create vaccination reminders error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء إشعارات للمخزون المنخفض
     * Create low stock notifications
     */
    public function createLowStockNotifications($threshold = 10) {
        try {
            // فحص مخزون اللقاحات
            $sql = "SELECT vs.*, v.name_ar, u.id as user_id
                    FROM vaccine_stock vs
                    JOIN vaccines v ON vs.vaccine_id = v.id
                    JOIN users u ON vs.user_id = u.id
                    WHERE vs.quantity <= ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$threshold]);
            $low_vaccine_stock = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($low_vaccine_stock as $stock) {
                $title = "تحذير: مخزون منخفض";
                $message = "مخزون لقاح {$stock['name_ar']} منخفض ({$stock['quantity']} وحدة متبقية)";
                
                $this->createNotification($stock['user_id'], $title, $message, 'warning');
            }
            
            // فحص مخزون الأدوية
            $sql = "SELECT ms.*, m.name, u.id as user_id
                    FROM medicine_stock ms
                    JOIN medicines m ON ms.medicine_id = m.id
                    JOIN users u ON ms.user_id = u.id
                    WHERE ms.quantity <= ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$threshold]);
            $low_medicine_stock = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($low_medicine_stock as $stock) {
                $title = "تحذير: مخزون منخفض";
                $message = "مخزون دواء {$stock['name']} منخفض ({$stock['quantity']} وحدة متبقية)";
                
                $this->createNotification($stock['user_id'], $title, $message, 'warning');
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('Create low stock notifications error: ' . $e->getMessage());
            return false;
        }
    }
}

/**
 * فئة إدارة الإحصائيات
 * Statistics Management Class
 */

class StatsManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على إحصائيات عامة
     * Get general statistics
     */
    public function getGeneralStats($user_id = null, $center_id = null) {
        try {
            $stats = [];
            
            // إحصائيات الأطفال
            $sql = "SELECT COUNT(*) FROM children";
            $params = [];
            
            if ($user_id) {
                $sql .= " WHERE nurse_id = ?";
                $params[] = $user_id;
            } elseif ($center_id) {
                $sql .= " WHERE center_id = ?";
                $params[] = $center_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['total_children'] = $stmt->fetchColumn();
            
            // إحصائيات التلقيحات المكتملة
            $sql = "SELECT COUNT(*) FROM child_vaccinations cv";
            
            if ($user_id) {
                $sql .= " JOIN children c ON cv.child_id = c.id WHERE c.nurse_id = ? AND cv.is_completed = 1";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " JOIN children c ON cv.child_id = c.id WHERE c.center_id = ? AND cv.is_completed = 1";
                $params = [$center_id];
            } else {
                $sql .= " WHERE cv.is_completed = 1";
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['completed_vaccinations'] = $stmt->fetchColumn();
            
            // إحصائيات هذا الشهر
            $sql = "SELECT COUNT(*) FROM children WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())";
            
            if ($user_id) {
                $sql .= " AND nurse_id = ?";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " AND center_id = ?";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['monthly_registrations'] = $stmt->fetchColumn();
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Get general stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * حفظ الإحصائيات الشهرية
     * Save monthly statistics
     */
    public function saveMonthlyStats($user_id, $month_key, $stats_data) {
        try {
            $sql = "INSERT INTO monthly_stats (user_id, month_key, total_children, total_vaccinations, completion_rate)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    total_children = VALUES(total_children),
                    total_vaccinations = VALUES(total_vaccinations),
                    completion_rate = VALUES(completion_rate)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $user_id,
                $month_key,
                $stats_data['total_children'] ?? 0,
                $stats_data['total_vaccinations'] ?? 0,
                $stats_data['completion_rate'] ?? 0
            ]);
            
        } catch (Exception $e) {
            error_log('Save monthly stats error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على الإحصائيات الشهرية
     * Get monthly statistics
     */
    public function getMonthlyStats($user_id, $months = 12) {
        try {
            $sql = "SELECT * FROM monthly_stats 
                    WHERE user_id = ? 
                    ORDER BY month_key DESC 
                    LIMIT ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $months]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get monthly stats error: ' . $e->getMessage());
            return [];
        }
    }
}
?>
