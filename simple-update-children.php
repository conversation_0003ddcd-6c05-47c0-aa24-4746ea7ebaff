<?php
/**
 * تحديث بسيط لجدول الأطفال - إضافة الأعمدة المطلوبة فقط
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $updates = [];
    
    // فحص الأعمدة الموجودة
    $stmt = $pdo->query("DESCRIBE children");
    $columns = $stmt->fetchAll();
    $existingColumns = array_column($columns, 'Field');
    
    // إضافة الأعمدة المطلوبة فقط (بدون تعديل center_id)
    if (!in_array('user_id', $existingColumns)) {
        try {
            $pdo->exec("ALTER TABLE children ADD COLUMN user_id VARCHAR(50)");
            $updates[] = 'تم إضافة عمود user_id';
        } catch (Exception $e) {
            $updates[] = 'خطأ في إضافة user_id: ' . $e->getMessage();
        }
    } else {
        $updates[] = 'عمود user_id موجود بالفعل';
    }
    
    if (!in_array('vaccination_dates', $existingColumns)) {
        try {
            $pdo->exec("ALTER TABLE children ADD COLUMN vaccination_dates TEXT");
            $updates[] = 'تم إضافة عمود vaccination_dates';
        } catch (Exception $e) {
            $updates[] = 'خطأ في إضافة vaccination_dates: ' . $e->getMessage();
        }
    } else {
        $updates[] = 'عمود vaccination_dates موجود بالفعل';
    }
    
    if (!in_array('completed_vaccinations', $existingColumns)) {
        try {
            $pdo->exec("ALTER TABLE children ADD COLUMN completed_vaccinations TEXT");
            $updates[] = 'تم إضافة عمود completed_vaccinations';
        } catch (Exception $e) {
            $updates[] = 'خطأ في إضافة completed_vaccinations: ' . $e->getMessage();
        }
    } else {
        $updates[] = 'عمود completed_vaccinations موجود بالفعل';
    }
    
    // إضافة فهارس
    try {
        $pdo->exec("CREATE INDEX idx_user_id ON children (user_id)");
        $updates[] = 'تم إضافة فهرس user_id';
    } catch (Exception $e) {
        $updates[] = 'فهرس user_id موجود بالفعل أو خطأ: ' . $e->getMessage();
    }
    
    // فحص الهيكل النهائي
    $stmt = $pdo->query("DESCRIBE children");
    $finalColumns = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'updates' => $updates,
        'final_columns' => $finalColumns,
        'message' => 'تم التحديث بنجاح'
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
