<?php
/**
 * حذف اللقاحات غير الصحيحة من قاعدة البيانات
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // قائمة اللقاحات غير الصحيحة المراد حذفها
    $incorrectVaccines = [
        'hepatitis_b_birth',
        'dtp_hib_hep_b_1',
        'dtp_hib_hep_b_2',
        'dtp_hib_hep_b_3',
        'pneumococcal_1',
        'measles_rubella_1',
        'meningococcal',
        'dtp_4',
        'measles_rubella_2',
        'pneumococcal_2',
        'pneumococcal_3'
    ];
    
    $results = [];
    $pdo->beginTransaction();
    
    foreach ($incorrectVaccines as $vaccineId) {
        $deletedCount = 0;
        
        // حذف من جدول vaccines إذا كان موجود
        try {
            $stmt = $pdo->prepare("DELETE FROM vaccines WHERE id = ?");
            $stmt->execute([$vaccineId]);
            $deletedFromVaccines = $stmt->rowCount();
            
            if ($deletedFromVaccines > 0) {
                $results[$vaccineId]['vaccines_table'] = "تم حذف $deletedFromVaccines سجل";
                $deletedCount += $deletedFromVaccines;
            } else {
                $results[$vaccineId]['vaccines_table'] = "غير موجود";
            }
        } catch (Exception $e) {
            $results[$vaccineId]['vaccines_table'] = "خطأ: " . $e->getMessage();
        }
        
        // حذف من جدول vaccine_stock إذا كان موجود
        try {
            $stmt = $pdo->prepare("DELETE FROM vaccine_stock WHERE vaccine_id = ?");
            $stmt->execute([$vaccineId]);
            $deletedFromStock = $stmt->rowCount();
            
            if ($deletedFromStock > 0) {
                $results[$vaccineId]['stock_table'] = "تم حذف $deletedFromStock سجل";
                $deletedCount += $deletedFromStock;
            } else {
                $results[$vaccineId]['stock_table'] = "غير موجود";
            }
        } catch (Exception $e) {
            $results[$vaccineId]['stock_table'] = "خطأ: " . $e->getMessage();
        }
        
        // حذف من جدول vaccine_usage_log إذا كان موجود
        try {
            $stmt = $pdo->prepare("DELETE FROM vaccine_usage_log WHERE vaccine_id = ?");
            $stmt->execute([$vaccineId]);
            $deletedFromUsage = $stmt->rowCount();
            
            if ($deletedFromUsage > 0) {
                $results[$vaccineId]['usage_table'] = "تم حذف $deletedFromUsage سجل";
                $deletedCount += $deletedFromUsage;
            } else {
                $results[$vaccineId]['usage_table'] = "غير موجود";
            }
        } catch (Exception $e) {
            $results[$vaccineId]['usage_table'] = "خطأ: " . $e->getMessage();
        }
        
        // حذف من جدول vaccine_list إذا كان موجود
        try {
            $stmt = $pdo->prepare("DELETE FROM vaccine_list WHERE id = ?");
            $stmt->execute([$vaccineId]);
            $deletedFromList = $stmt->rowCount();
            
            if ($deletedFromList > 0) {
                $results[$vaccineId]['list_table'] = "تم حذف $deletedFromList سجل";
                $deletedCount += $deletedFromList;
            } else {
                $results[$vaccineId]['list_table'] = "غير موجود";
            }
        } catch (Exception $e) {
            $results[$vaccineId]['list_table'] = "خطأ: " . $e->getMessage();
        }
        
        // تنظيف بيانات التخطيط الشهري
        try {
            $stmt = $pdo->prepare("
                SELECT id, vaccines_data FROM vaccine_monthly_planning 
                WHERE vaccines_data LIKE ?
            ");
            $stmt->execute(['%"' . $vaccineId . '"%']);
            $planningRecords = $stmt->fetchAll();
            
            $updatedPlanning = 0;
            foreach ($planningRecords as $record) {
                $vaccinesData = json_decode($record['vaccines_data'], true);
                if (is_array($vaccinesData) && isset($vaccinesData[$vaccineId])) {
                    unset($vaccinesData[$vaccineId]);
                    
                    $updateStmt = $pdo->prepare("
                        UPDATE vaccine_monthly_planning 
                        SET vaccines_data = ? 
                        WHERE id = ?
                    ");
                    $updateStmt->execute([
                        json_encode($vaccinesData, JSON_UNESCAPED_UNICODE),
                        $record['id']
                    ]);
                    $updatedPlanning++;
                }
            }
            
            if ($updatedPlanning > 0) {
                $results[$vaccineId]['planning_table'] = "تم تنظيف $updatedPlanning سجل تخطيط";
                $deletedCount += $updatedPlanning;
            } else {
                $results[$vaccineId]['planning_table'] = "غير موجود في التخطيط";
            }
        } catch (Exception $e) {
            $results[$vaccineId]['planning_table'] = "خطأ: " . $e->getMessage();
        }
        
        $results[$vaccineId]['total_affected'] = $deletedCount;
    }
    
    $pdo->commit();
    
    // عرض اللقاحات المتبقية الصحيحة
    $stmt = $pdo->query("SELECT id, name_ar FROM vaccines ORDER BY id");
    $remainingVaccines = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف اللقاحات غير الصحيحة بنجاح',
        'deleted_vaccines' => $results,
        'remaining_vaccines' => $remainingVaccines,
        'total_deleted' => count($incorrectVaccines)
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => basename(__FILE__),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
