<?php
/**
 * فحص المستخدمين الموجودين
 */

require_once 'config/database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // فحص بنية جدول المستخدمين أولاً
    echo "📋 بنية جدول المستخدمين:\n";
    $stmt = $conn->query('DESCRIBE users');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    echo "\n";

    // عرض جميع المستخدمين
    $stmt = $conn->query('SELECT * FROM users ORDER BY id');
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "👥 المستخدمون الموجودون:\n";
    echo "=====================================\n";
    
    if (empty($users)) {
        echo "❌ لا يوجد مستخدمون في قاعدة البيانات\n";
    } else {
        foreach ($users as $user) {
            echo "🆔 ID: {$user['id']}\n";
            foreach ($user as $key => $value) {
                if ($key !== 'id') {
                    echo "📝 {$key}: {$value}\n";
                }
            }
            echo "-------------------------------------\n";
        }
    }
    
    echo "\n📊 إجمالي عدد المستخدمين: " . count($users) . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
