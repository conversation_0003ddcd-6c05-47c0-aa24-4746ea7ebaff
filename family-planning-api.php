<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين إعدادات قاعدة البيانات
require_once 'config/database-live.php';

try {
    // الاتصال بقاعدة البيانات باستخدام الإعدادات المناسبة
    $pdo = getDatabaseConnection();

    // قراءة البيانات المرسلة
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('خطأ في تحليل JSON: ' . json_last_error_msg());
    }

    if (!$input) {
        $input = $_POST;
    }

    $action = $input['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('لم يتم تحديد الإجراء المطلوب');
    }
    
    // إنشاء الجداول عند الحاجة (مع معالجة الأخطاء)
    try {
        createFamilyPlanningTables($pdo);
    } catch (Exception $e) {
        error_log("Warning: Could not create family planning tables: " . $e->getMessage());
        // المتابعة حتى لو فشل إنشاء الجداول
    }
    
    switch ($action) {
        case 'test':
            // اختبار اتصال قاعدة البيانات
            try {
                $stmt = $pdo->query("SELECT 1 as test");
                $result = $stmt->fetch();
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Family Planning API يعمل بشكل صحيح',
                    'database' => 'متصل',
                    'test_query' => $result ? 'نجح' : 'فشل',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'user_id' => $input['user_id'] ?? 'غير محدد'
                ], JSON_UNESCAPED_UNICODE);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Family Planning API يعمل لكن قاعدة البيانات بها مشكلة',
                    'database_error' => $e->getMessage(),
                    'timestamp' => date('Y-m-d H:i:s')
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'load':
        case 'load_contraceptive_list':
            loadContraceptiveListAPI($pdo, $input);
            break;
            
        case 'add_contraceptive':
            addContraceptiveAPI($pdo, $input);
            break;
            
        case 'delete_contraceptive':
            deleteContraceptiveAPI($pdo, $input);
            break;
            
        case 'save_family_planning_monthly_planning':
            saveFamilyPlanningMonthlyPlanningAPI($pdo, $input);
            break;
            
        case 'load_family_planning_monthly_planning':
            loadFamilyPlanningMonthlyPlanningAPI($pdo, $input);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    error_log('خطأ في قاعدة البيانات family-planning-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_type' => 'database',
        'file' => 'family-planning-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    error_log('خطأ عام في family-planning-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_type' => 'general',
        'file' => 'family-planning-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

// ===============================
// دوال إدارة تنظيم الأسرة
// ===============================

// إنشاء جداول تنظيم الأسرة
function createFamilyPlanningTables($pdo) {
    try {
        // جدول قائمة تقنيات منع الحمل
        $sql1 = "
        CREATE TABLE IF NOT EXISTS contraceptive_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // جدول التخطيط الشهري لتنظيم الأسرة
        $sql2 = "
        CREATE TABLE IF NOT EXISTS family_planning_monthly_planning (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            month_key VARCHAR(20) NOT NULL,
            month_name VARCHAR(100),
            contraceptives_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_month (user_id, month_key),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // جدول مخزون تقنيات منع الحمل
        $sql3 = "
        CREATE TABLE IF NOT EXISTS contraceptive_stock (
            id INT AUTO_INCREMENT PRIMARY KEY,
            contraceptive_id VARCHAR(50),
            user_id VARCHAR(50),
            center_id VARCHAR(50),
            quantity INT DEFAULT 0,
            expiry_date DATE,
            batch_number VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_contraceptive_user (contraceptive_id, user_id),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        $pdo->exec($sql1);
        $pdo->exec($sql2);
        $pdo->exec($sql3);
        
    } catch (Exception $e) {
        error_log('خطأ في إنشاء جداول تنظيم الأسرة: ' . $e->getMessage());
    }
}

// تحميل قائمة تقنيات منع الحمل
function loadContraceptiveListAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    try {
        // جلب قائمة وسائل منع الحمل أولاً
        $stmt = $pdo->prepare("SELECT id, name FROM contraceptive_list WHERE user_id = ? ORDER BY name");
        $stmt->execute([$user_id]);
        $contraceptiveList = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب التخطيط الشهري للأشهر الثلاثة
        $stmt = $pdo->prepare("SELECT contraceptives_data FROM family_planning_monthly_planning WHERE user_id = ? ORDER BY month_key");
        $stmt->execute([$user_id]);
        $planningResults = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تجميع الكميات من التخطيط الشهري
        $contraceptiveQuantities = [];
        foreach ($planningResults as $planning) {
            $contraceptivesData = json_decode($planning['contraceptives_data'], true);
            if ($contraceptivesData && is_array($contraceptivesData)) {
                // البيانات محفوظة بتنسيق {id: quantity}
                foreach ($contraceptivesData as $contraceptiveId => $quantity) {
                    $quantity = intval($quantity);

                    if ($contraceptiveId && $quantity > 0) {
                        if (!isset($contraceptiveQuantities[$contraceptiveId])) {
                            $contraceptiveQuantities[$contraceptiveId] = 0;
                        }
                        $contraceptiveQuantities[$contraceptiveId] += $quantity;
                    }
                }
            }
        }

        // دمج قائمة وسائل منع الحمل مع الكميات من التخطيط
        $contraceptives = [];
        foreach ($contraceptiveList as $contraceptive) {
            $contraceptives[] = [
                'id' => $contraceptive['id'],
                'name' => $contraceptive['name'],
                'quantity' => $contraceptiveQuantities[$contraceptive['id']] ?? 0
            ];
        }

        // تسجيل للتشخيص
        error_log("Contraceptives query executed for user: $user_id, found: " . count($contraceptives) . " contraceptives from planning data");
        
        // إذا لم توجد وسائل منع الحمل، إرجاع قائمة فارغة مع رسالة
        if (empty($contraceptives)) {
            echo json_encode([
                'success' => true,
                'contraceptives' => [],
                'count' => 0,
                'message' => 'لا توجد وسائل منع الحمل مسجلة لهذا المستخدم'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                'success' => true,
                'contraceptives' => $contraceptives,
                'count' => count($contraceptives),
                'message' => 'تم جلب قائمة وسائل منع الحمل بنجاح'
            ], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        throw $e;
    }
}

// إضافة تقنية منع حمل جديدة
function addContraceptiveAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '1';
    $name = trim($input['name'] ?? '');
    
    if (!$user_id || !$name) {
        throw new Exception('جميع البيانات مطلوبة: معرف المستخدم، اسم التقنية');
    }
    
    try {
        // إنشاء معرف فريد للتقنية
        $contraceptive_id = time() . rand(1000, 9999);
        
        $stmt = $pdo->prepare("
            INSERT INTO contraceptive_list (id, user_id, center_id, name) 
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([$contraceptive_id, $user_id, $center_id, $name]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة التقنية بنجاح',
            'contraceptive' => [
                'id' => $contraceptive_id,
                'name' => $name
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}

// حفظ التخطيط الشهري لتنظيم الأسرة
function saveFamilyPlanningMonthlyPlanningAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '1';
    $planning = $input['planning'] ?? [];
    
    if (!$user_id || !is_array($planning)) {
        throw new Exception('معرف المستخدم وبيانات التخطيط مطلوبان');
    }
    
    try {
        $pdo->beginTransaction();
        
        $saved_months = [];
        
        foreach ($planning as $month_key => $month_data) {
            $month_name = $month_data['name'] ?? '';
            $contraceptives_data = json_encode($month_data['contraceptives'] ?? [], JSON_UNESCAPED_UNICODE);
            
            $stmt = $pdo->prepare("
                INSERT INTO family_planning_monthly_planning (user_id, center_id, month_key, month_name, contraceptives_data) 
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                month_name = VALUES(month_name),
                contraceptives_data = VALUES(contraceptives_data),
                updated_at = CURRENT_TIMESTAMP
            ");
            
            $result = $stmt->execute([$user_id, $center_id, $month_key, $month_name, $contraceptives_data]);
            
            if ($result) {
                $saved_months[] = $month_key;
            }
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ التخطيط الشهري لتنظيم الأسرة بنجاح',
            'saved_months' => $saved_months,
            'user_id' => $user_id
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

// تحميل التخطيط الشهري لتنظيم الأسرة
function loadFamilyPlanningMonthlyPlanningAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        echo json_encode([
            'success' => true,
            'planning' => [],
            'is_default' => true
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("SELECT month_key, month_name, contraceptives_data FROM family_planning_monthly_planning WHERE user_id = ? ORDER BY month_key");
        $stmt->execute([$user_id]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($results)) {
            echo json_encode([
                'success' => true,
                'planning' => [],
                'is_default' => true
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        
        $planning = [];
        foreach ($results as $row) {
            $contraceptives_data = json_decode($row['contraceptives_data'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $contraceptives_data = [];
            }
            
            $planning[$row['month_key']] = [
                'name' => $row['month_name'] ?? '',
                'contraceptives' => $contraceptives_data
            ];
        }
        
        echo json_encode([
            'success' => true,
            'planning' => $planning,
            'loaded_months' => array_keys($planning)
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}

// حذف تقنية منع حمل
function deleteContraceptiveAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $contraceptive_id = $input['contraceptive_id'] ?? '';
    
    if (!$user_id || !$contraceptive_id) {
        throw new Exception('معرف المستخدم ومعرف التقنية مطلوبان');
    }
    
    try {
        $pdo->beginTransaction();
        
        // حذف التقنية من القائمة
        $stmt = $pdo->prepare("DELETE FROM contraceptive_list WHERE id = ? AND user_id = ?");
        $stmt->execute([$contraceptive_id, $user_id]);
        
        // حذف التقنية من المخزون
        $stmt = $pdo->prepare("DELETE FROM contraceptive_stock WHERE contraceptive_id = ? AND user_id = ?");
        $stmt->execute([$contraceptive_id, $user_id]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف التقنية بنجاح',
            'contraceptive_id' => $contraceptive_id
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

?>
