# نظام إدارة المراكز الصحية - استعادة قاعدة البيانات

## 📋 نظرة عامة

هذا المشروع يحتوي على جميع الملفات والأدوات اللازمة لاستعادة قاعدة البيانات المحذوفة لنظام إدارة المراكز الصحية. تم تحليل جميع ملفات المشروع الأصلية واستنتاج بنية قاعدة البيانات الكاملة.

## 🎯 ما تم إنجازه

✅ **تحليل شامل للمشروع**
- فحص جميع ملفات PHP وAPI
- استنتاج بنية قاعدة البيانات من الأكواد
- اكتشاف 22 جدول (17 أساسي + 5 إضافي)

✅ **إنشاء ملفات الاستعادة**
- ملف SQL كامل للاستعادة
- سكريبت تلقائي للاستعادة
- دليل مفصل خطوة بخطوة

✅ **أدوات مساعدة**
- سكريبت النسخ الاحتياطي التلقائي
- أدوات فحص سلامة البيانات
- دليل استكشاف الأخطاء

## 📁 الملفات المتوفرة

### ملفات الاستعادة الرئيسية:
- **`database_complete_rebuild.sql`** - ملف SQL كامل لإعادة إنشاء قاعدة البيانات
- **`restore_database.sh`** - سكريبت تلقائي للاستعادة
- **`QUICK_START.md`** - دليل البدء السريع

### ملفات التوثيق:
- **`CloudPanel_Restoration_Guide.md`** - دليل مخصص لـ CloudPanel
- **`database_restoration_guide.md`** - دليل شامل للاستعادة
- **`database_analysis_report.md`** - تقرير تحليل المشروع
- **`README.md`** - هذا الملف

### ملفات المشروع الأصلية:
- **`database_design.sql`** - ملف التصميم الأصلي (محدث)
- **`config/`** - ملفات إعدادات قاعدة البيانات
- **`api/`** - ملفات API والفئات
- ملفات PHP الأخرى للمشروع

## 🚀 البدء السريع

### الطريقة الأولى: السكريبت التلقائي (الأسهل)
```bash
# منح صلاحية التنفيذ
chmod +x restore_database.sh

# تشغيل السكريبت
./restore_database.sh
```

### الطريقة الثانية: يدوياً
```bash
# إنشاء قاعدة البيانات
mysql -h 127.0.0.1 -u csdbuser -p -e "CREATE DATABASE IF NOT EXISTS csdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# استيراد البيانات
mysql -h 127.0.0.1 -u csdbuser -p csdb < database_complete_rebuild.sql
```

## 🗄️ معلومات قاعدة البيانات (CloudPanel)

- **Host**: `127.0.0.1`
- **Port**: `3306`
- **اسم قاعدة البيانات**: `csdb`
- **المستخدم**: `csdbuser`
- **كلمة المرور**: `j5aKN6lz5bsujTcWaYAd`
- **الترميز**: `utf8mb4_unicode_ci`
- **عدد الجداول**: 22 جدول

## 📊 الجداول المتضمنة

### الجداول الأساسية (17):
1. **centers** - المراكز الصحية
2. **users** - المستخدمين
3. **children** - الأطفال
4. **vaccines** - اللقاحات
5. **vaccine_stock** - مخزون اللقاحات
6. **child_vaccinations** - تلقيحات الأطفال
7. **vaccine_usage_log** - سجل استخدام اللقاحات
8. **medicines** - الأدوية
9. **medicine_stock** - مخزون الأدوية
10. **contraceptives** - وسائل منع الحمل
11. **contraceptive_stock** - مخزون وسائل منع الحمل
12. **monthly_planning** - التخطيط الشهري
13. **messages** - الرسائل
14. **tasks** - المهام
15. **notifications** - الإشعارات
16. **monthly_stats** - الإحصائيات الشهرية
17. **user_settings** - إعدادات المستخدم

### الجداول الإضافية (5):
18. **vaccine_list** - قوائم اللقاحات المخصصة
19. **medicine_list** - قوائم الأدوية المخصصة
20. **contraceptive_list** - قوائم وسائل منع الحمل المخصصة
21. **vaccine_monthly_planning** - التخطيط الشهري للقاحات
22. **family_planning_monthly_planning** - التخطيط الشهري لتنظيم الأسرة

## 📋 البيانات الافتراضية

### المراكز الصحية (3):
- المركز الصحي الرئيسي
- مركز صحة الأطفال
- مركز الرعاية الأولية

### اللقاحات (19):
- لقاح السل (BCG)
- التهاب الكبد ب
- شلل الأطفال (4 جرعات)
- الثلاثي + المستدمية النزلية + التهاب الكبد ب (3 جرعات)
- المكورات الرئوية (3 جرعات)
- فيروس الروتا (3 جرعات)
- الحصبة والحصبة الألمانية (2 جرعة)
- المكورات السحائية

### الأدوية (8):
- شراب الباراسيتامول
- شراب الأموكسيسيلين
- شراب الحديد
- قطرات فيتامين د
- أملاح الإماهة الفموية
- أقراص الزنك
- أقراص الألبيندازول
- أقراص الميبيندازول

### وسائل منع الحمل (10):
- حبوب منع الحمل المركبة
- حبوب البروجستين
- اللولب النحاسي والهرموني
- حقنة 3 أشهر
- الغرسة
- الواقي الذكري والأنثوي
- الحجاب الحاجز
- حبة الطوارئ

### المستخدمين الافتراضيين (4):
- مدير النظام (admin/admin)
- الممرضة فاطمة أحمد (nurse1)
- الممرضة عائشة محمد (nurse2)
- المشرفة خديجة علي (supervisor1)

## ✅ التحقق من النجاح

بعد الاستعادة، تحقق من:
```sql
-- عدد الجداول (يجب أن يكون 22)
SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='csdb';

-- البيانات الأساسية
SELECT COUNT(*) as users FROM users;        -- 4
SELECT COUNT(*) as vaccines FROM vaccines;  -- 19
SELECT COUNT(*) as centers FROM centers;    -- 3
```

## 🔧 خيارات السكريبت التلقائي

```bash
# عرض المساعدة
./restore_database.sh --help

# فحص الاتصال فقط
./restore_database.sh -c

# إنشاء نسخة احتياطية قبل الاستعادة
./restore_database.sh -b

# تحديد معاملات مخصصة
./restore_database.sh -h 127.0.0.1 -d csdb -u csdbuser -p password
```

## 🆘 استكشاف الأخطاء

### المشاكل الشائعة:
1. **خطأ في الاتصال**: تحقق من معاملات الاتصال
2. **خطأ في الصلاحيات**: امنح صلاحيات إضافية للمستخدم
3. **خطأ في الترميز**: تأكد من دعم utf8mb4

### ملفات المساعدة:
- `import_errors.log` - سجل أخطاء الاستيراد
- `database_restoration_guide.md` - دليل مفصل لحل المشاكل

## 📞 الدعم والمساعدة

للحصول على مساعدة مفصلة:
1. راجع `QUICK_START.md` للبدء السريع
2. راجع `CloudPanel_Restoration_Guide.md` للدليل المخصص لـ CloudPanel
3. راجع `database_restoration_guide.md` للدليل الشامل
4. راجع `database_analysis_report.md` لفهم بنية قاعدة البيانات

## 🔒 الأمان

- تم تشفير كلمات المرور باستخدام `password_hash()`
- يُنصح بتغيير كلمات المرور الافتراضية
- إعداد صلاحيات محدودة للمستخدمين
- تفعيل النسخ الاحتياطي التلقائي

## 📝 ملاحظات مهمة

1. **الترميز**: تم استخدام `utf8mb4_unicode_ci` لدعم النصوص العربية
2. **التوافق**: متوافق مع MySQL 5.7+ و MariaDB 10.2+
3. **الأداء**: تم إضافة فهارس مناسبة لتحسين الأداء
4. **العلاقات**: تم تعريف جميع العلاقات مع `FOREIGN KEY` constraints

---

## 🎉 تم بنجاح!

الآن لديك كل ما تحتاجه لاستعادة قاعدة البيانات المحذوفة بالكامل. ابدأ بتشغيل السكريبت التلقائي أو اتبع الدليل المفصل حسب احتياجاتك.
