<?php
/**
 * فئة إدارة المستخدمين
 * User Management Class
 */

class UserManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على جميع المستخدمين
     * Get all users
     */
    public function getAllUsers($center_id = null) {
        try {
            $sql = "SELECT u.id, u.username, u.name, u.role, u.is_active, u.created_at, 
                           c.name as center_name, u.center_id
                    FROM users u 
                    LEFT JOIN centers c ON u.center_id = c.id";
            
            $params = [];
            
            if ($center_id) {
                $sql .= " WHERE u.center_id = ?";
                $params[] = $center_id;
            }
            
            $sql .= " ORDER BY u.created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get all users error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مستخدم بالمعرف
     * Get user by ID
     */
    public function getUserById($id) {
        try {
            $sql = "SELECT u.id, u.username, u.name, u.role, u.is_active, u.created_at, 
                           c.name as center_name, u.center_id
                    FROM users u 
                    LEFT JOIN centers c ON u.center_id = c.id 
                    WHERE u.id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get user by ID error: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * إنشاء مستخدم جديد
     * Create new user
     */
    public function createUser($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['username', 'password', 'name', 'center_id'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("الحقل {$field} مطلوب");
                }
            }
            
            // التحقق من عدم تكرار اسم المستخدم
            if ($this->usernameExists($data['username'])) {
                throw new Exception('اسم المستخدم موجود مسبقاً');
            }
            
            // تشفير كلمة المرور
            $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // إنشاء معرف فريد
            $user_id = $this->generateUserId();
            
            $sql = "INSERT INTO users (id, username, password, name, center_id, role, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $user_id,
                $data['username'],
                $hashed_password,
                $data['name'],
                $data['center_id'],
                $data['role'] ?? 'nurse',
                $data['is_active'] ?? 1
            ]);
            
            if ($result) {
                return $this->getUserById($user_id);
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Create user error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث مستخدم
     * Update user
     */
    public function updateUser($id, $data) {
        try {
            // التحقق من وجود المستخدم
            $existing_user = $this->getUserById($id);
            if (!$existing_user) {
                throw new Exception('المستخدم غير موجود');
            }
            
            // بناء استعلام التحديث
            $update_fields = [];
            $params = [];
            
            if (isset($data['username']) && !empty($data['username'])) {
                // التحقق من عدم تكرار اسم المستخدم
                if ($data['username'] !== $existing_user['username'] && $this->usernameExists($data['username'])) {
                    throw new Exception('اسم المستخدم موجود مسبقاً');
                }
                $update_fields[] = "username = ?";
                $params[] = $data['username'];
            }
            
            if (isset($data['name']) && !empty($data['name'])) {
                $update_fields[] = "name = ?";
                $params[] = $data['name'];
            }
            
            if (isset($data['center_id'])) {
                $update_fields[] = "center_id = ?";
                $params[] = $data['center_id'];
            }
            
            if (isset($data['role'])) {
                $update_fields[] = "role = ?";
                $params[] = $data['role'];
            }
            
            if (isset($data['is_active'])) {
                $update_fields[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (isset($data['password']) && !empty($data['password'])) {
                $update_fields[] = "password = ?";
                $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }
            
            if (empty($update_fields)) {
                throw new Exception('لا توجد بيانات للتحديث');
            }
            
            $update_fields[] = "updated_at = CURRENT_TIMESTAMP";
            $params[] = $id;
            
            $sql = "UPDATE users SET " . implode(', ', $update_fields) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $this->getUserById($id);
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Update user error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف مستخدم
     * Delete user
     */
    public function deleteUser($id) {
        try {
            // التحقق من وجود المستخدم
            $user = $this->getUserById($id);
            if (!$user) {
                throw new Exception('المستخدم غير موجود');
            }
            
            // بدء معاملة
            $this->db->beginTransaction();
            
            // حذف البيانات المرتبطة أولاً
            $this->deleteUserRelatedData($id);
            
            // حذف المستخدم
            $sql = "DELETE FROM users WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->db->commit();
                return true;
            } else {
                $this->db->rollback();
                return false;
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log('Delete user error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف البيانات المرتبطة بالمستخدم
     * Delete user related data
     */
    private function deleteUserRelatedData($user_id) {
        try {
            // حذف مخزون اللقاحات
            $sql = "DELETE FROM vaccine_stock WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // حذف مخزون الأدوية
            $sql = "DELETE FROM medicine_stock WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // حذف مخزون وسائل منع الحمل
            $sql = "DELETE FROM contraceptive_stock WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // حذف التخطيط الشهري
            $sql = "DELETE FROM monthly_planning WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // حذف المهام
            $sql = "DELETE FROM tasks WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // حذف الإشعارات
            $sql = "DELETE FROM notifications WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // حذف الإحصائيات الشهرية
            $sql = "DELETE FROM monthly_stats WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // حذف إعدادات المستخدم
            $sql = "DELETE FROM user_settings WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            // تحديث الأطفال لإزالة الممرض المحذوف
            $sql = "UPDATE children SET nurse_id = NULL WHERE nurse_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
        } catch (Exception $e) {
            error_log('Delete user related data error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * التحقق من وجود اسم المستخدم
     * Check if username exists
     */
    private function usernameExists($username) {
        try {
            $sql = "SELECT COUNT(*) FROM users WHERE username = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$username]);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log('Username exists check error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء معرف مستخدم فريد
     * Generate unique user ID
     */
    private function generateUserId() {
        do {
            $id = 'user_' . uniqid();
            $sql = "SELECT COUNT(*) FROM users WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
        } while ($stmt->fetchColumn() > 0);
        
        return $id;
    }
    
    /**
     * الحصول على المراكز الصحية
     * Get health centers
     */
    public function getHealthCenters() {
        try {
            $sql = "SELECT * FROM centers ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get health centers error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إحصائيات المستخدمين
     * User statistics
     */
    public function getUserStats($center_id = null) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_users,
                        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
                        SUM(CASE WHEN role = 'supervisor' THEN 1 ELSE 0 END) as supervisor_count,
                        SUM(CASE WHEN role = 'nurse' THEN 1 ELSE 0 END) as nurse_count,
                        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count,
                        SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_count
                    FROM users";
            
            $params = [];
            
            if ($center_id) {
                $sql .= " WHERE center_id = ?";
                $params[] = $center_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get user stats error: ' . $e->getMessage());
            return [];
        }
    }
}
?>
