<?php
/**
 * إعدادات قاعدة البيانات للخادم المباشر
 * Live Server Database Configuration
 */

// تحديد إعدادات قاعدة البيانات بناءً على البيئة
$isLiveServer = isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'csmanager.online') !== false;

if ($isLiveServer) {
    // إعدادات الخادم المباشر - CloudPanel
    $db_configs = [
        [
            'host' => '127.0.0.1',  // CloudPanel Host
            'dbname' => 'csdb',
            'username' => 'csdbuser',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ],
        [
            'host' => 'localhost',  // احتياطي
            'dbname' => 'csdb',
            'username' => 'csdbuser',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ]
    ];
} else {
    // إعدادات الخادم المحلي
    $db_configs = [
        [
            'host' => '127.0.0.1',
            'dbname' => 'csdb',
            'username' => 'csdbuser',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ]
    ];
}

/**
 * إنشاء اتصال قاعدة البيانات مع تجريب عدة إعدادات
 */
function getDatabaseConnection() {
    global $db_configs;

    $lastError = null;

    // جرب كل إعداد حتى ينجح واحد
    foreach ($db_configs as $config) {
        try {
            $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]);

            // اختبار الاتصال
            $pdo->query("SELECT 1");

            // إذا وصلنا هنا، الاتصال نجح
            error_log("Database connection successful with: " . $config['host'] . "/" . $config['dbname']);
            return $pdo;

        } catch (PDOException $e) {
            $lastError = $e;
            error_log("Database connection failed with {$config['host']}/{$config['dbname']}: " . $e->getMessage());
            continue;
        }
    }

    // إذا فشلت جميع المحاولات
    throw new Exception("فشل في الاتصال بقاعدة البيانات مع جميع الإعدادات. آخر خطأ: " . ($lastError ? $lastError->getMessage() : 'غير معروف'));
}

/**
 * اختبار الاتصال بقاعدة البيانات
 */
function testDatabaseConnection() {
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        error_log("Database test failed: " . $e->getMessage());
        return false;
    }
}
?>
