<?php
/**
 * فئة إدارة الإحصائيات
 * Statistics Management Class
 */

class StatsManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على إحصائيات عامة
     * Get general statistics
     */
    public function getGeneralStats($user_id = null, $center_id = null) {
        try {
            $stats = [];
            
            // إحصائيات الأطفال
            $sql = "SELECT COUNT(*) FROM children";
            $params = [];
            
            if ($user_id) {
                $sql .= " WHERE nurse_id = ?";
                $params[] = $user_id;
            } elseif ($center_id) {
                $sql .= " WHERE center_id = ?";
                $params[] = $center_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['total_children'] = $stmt->fetchColumn();
            
            // إحصائيات التلقيحات المكتملة
            $sql = "SELECT COUNT(*) FROM child_vaccinations cv";
            
            if ($user_id) {
                $sql .= " JOIN children c ON cv.child_id = c.id WHERE c.nurse_id = ? AND cv.is_completed = 1";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " JOIN children c ON cv.child_id = c.id WHERE c.center_id = ? AND cv.is_completed = 1";
                $params = [$center_id];
            } else {
                $sql .= " WHERE cv.is_completed = 1";
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['completed_vaccinations'] = $stmt->fetchColumn();
            
            // إحصائيات هذا الشهر
            $sql = "SELECT COUNT(*) FROM children WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())";
            
            if ($user_id) {
                $sql .= " AND nurse_id = ?";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " AND center_id = ?";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['monthly_registrations'] = $stmt->fetchColumn();
            
            // إحصائيات التلقيحات المتأخرة
            $sql = "SELECT COUNT(*) FROM child_vaccinations cv";
            
            if ($user_id) {
                $sql .= " JOIN children c ON cv.child_id = c.id WHERE c.nurse_id = ? AND cv.is_completed = 0 AND cv.due_date < CURRENT_DATE()";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " JOIN children c ON cv.child_id = c.id WHERE c.center_id = ? AND cv.is_completed = 0 AND cv.due_date < CURRENT_DATE()";
                $params = [$center_id];
            } else {
                $sql .= " WHERE cv.is_completed = 0 AND cv.due_date < CURRENT_DATE()";
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['overdue_vaccinations'] = $stmt->fetchColumn();
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Get general stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * حفظ الإحصائيات الشهرية
     * Save monthly statistics
     */
    public function saveMonthlyStats($user_id, $month_key, $stats_data) {
        try {
            $sql = "INSERT INTO monthly_stats (user_id, month_key, total_children, total_vaccinations, completion_rate)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    total_children = VALUES(total_children),
                    total_vaccinations = VALUES(total_vaccinations),
                    completion_rate = VALUES(completion_rate)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $user_id,
                $month_key,
                $stats_data['total_children'] ?? 0,
                $stats_data['total_vaccinations'] ?? 0,
                $stats_data['completion_rate'] ?? 0
            ]);
            
        } catch (Exception $e) {
            error_log('Save monthly stats error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على الإحصائيات الشهرية
     * Get monthly statistics
     */
    public function getMonthlyStats($user_id, $months = 12) {
        try {
            $sql = "SELECT * FROM monthly_stats 
                    WHERE user_id = ? 
                    ORDER BY month_key DESC 
                    LIMIT ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $months]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get monthly stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات التلقيحات حسب العمر
     * Get vaccination statistics by age
     */
    public function getVaccinationStatsByAge($center_id = null) {
        try {
            $sql = "SELECT 
                        v.age_months,
                        v.name_ar,
                        COUNT(cv.id) as total_scheduled,
                        SUM(CASE WHEN cv.is_completed = 1 THEN 1 ELSE 0 END) as completed,
                        ROUND((SUM(CASE WHEN cv.is_completed = 1 THEN 1 ELSE 0 END) / COUNT(cv.id)) * 100, 2) as completion_rate
                    FROM vaccines v
                    LEFT JOIN child_vaccinations cv ON v.id = cv.vaccine_id";
            
            if ($center_id) {
                $sql .= " LEFT JOIN children c ON cv.child_id = c.id WHERE c.center_id = ?";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $sql .= " GROUP BY v.id, v.age_months, v.name_ar ORDER BY v.age_months";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get vaccination stats by age error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات المراكز
     * Get centers statistics
     */
    public function getCentersStats() {
        try {
            $sql = "SELECT 
                        c.id,
                        c.name,
                        COUNT(DISTINCT u.id) as total_users,
                        COUNT(DISTINCT ch.id) as total_children,
                        COUNT(DISTINCT cv.id) as total_vaccinations,
                        SUM(CASE WHEN cv.is_completed = 1 THEN 1 ELSE 0 END) as completed_vaccinations
                    FROM centers c
                    LEFT JOIN users u ON c.id = u.center_id
                    LEFT JOIN children ch ON c.id = ch.center_id
                    LEFT JOIN child_vaccinations cv ON ch.id = cv.child_id
                    GROUP BY c.id, c.name
                    ORDER BY c.name";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get centers stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات الأداء الشهري
     * Get monthly performance statistics
     */
    public function getMonthlyPerformance($year = null, $center_id = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }
            
            $sql = "SELECT 
                        MONTH(ch.created_at) as month_number,
                        MONTHNAME(ch.created_at) as month_name,
                        COUNT(DISTINCT ch.id) as new_children,
                        COUNT(DISTINCT cv.id) as total_vaccinations,
                        SUM(CASE WHEN cv.is_completed = 1 THEN 1 ELSE 0 END) as completed_vaccinations
                    FROM children ch
                    LEFT JOIN child_vaccinations cv ON ch.id = cv.child_id
                    WHERE YEAR(ch.created_at) = ?";
            
            $params = [$year];
            
            if ($center_id) {
                $sql .= " AND ch.center_id = ?";
                $params[] = $center_id;
            }
            
            $sql .= " GROUP BY MONTH(ch.created_at), MONTHNAME(ch.created_at)
                      ORDER BY MONTH(ch.created_at)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get monthly performance error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات المخزون
     * Get stock statistics
     */
    public function getStockStats($center_id = null) {
        try {
            $stats = [];
            
            // إحصائيات اللقاحات
            $sql = "SELECT 
                        COUNT(DISTINCT vs.vaccine_id) as vaccine_types,
                        SUM(vs.quantity) as total_vaccine_stock,
                        COUNT(CASE WHEN vs.expiry_date < CURRENT_DATE() THEN 1 END) as expired_vaccines
                    FROM vaccine_stock vs";
            
            if ($center_id) {
                $sql .= " JOIN users u ON vs.user_id = u.id WHERE u.center_id = ?";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['vaccines'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // إحصائيات الأدوية
            $sql = "SELECT 
                        COUNT(DISTINCT ms.medicine_id) as medicine_types,
                        SUM(ms.quantity) as total_medicine_stock,
                        COUNT(CASE WHEN ms.expiry_date < CURRENT_DATE() THEN 1 END) as expired_medicines
                    FROM medicine_stock ms";
            
            if ($center_id) {
                $sql .= " JOIN users u ON ms.user_id = u.id WHERE u.center_id = ?";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['medicines'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // إحصائيات وسائل منع الحمل
            $sql = "SELECT 
                        COUNT(DISTINCT cs.contraceptive_id) as contraceptive_types,
                        SUM(cs.quantity) as total_contraceptive_stock,
                        COUNT(CASE WHEN cs.expiry_date < CURRENT_DATE() THEN 1 END) as expired_contraceptives
                    FROM contraceptive_stock cs";
            
            if ($center_id) {
                $sql .= " JOIN users u ON cs.user_id = u.id WHERE u.center_id = ?";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['contraceptives'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Get stock stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إنشاء تقرير شامل
     * Generate comprehensive report
     */
    public function generateComprehensiveReport($center_id = null, $start_date = null, $end_date = null) {
        try {
            $report = [];
            
            // الإحصائيات العامة
            $report['general'] = $this->getGeneralStats(null, $center_id);
            
            // إحصائيات التلقيحات حسب العمر
            $report['vaccinations_by_age'] = $this->getVaccinationStatsByAge($center_id);
            
            // إحصائيات المخزون
            $report['stock'] = $this->getStockStats($center_id);
            
            // الأداء الشهري
            $report['monthly_performance'] = $this->getMonthlyPerformance(date('Y'), $center_id);
            
            // معلومات التقرير
            $report['report_info'] = [
                'generated_at' => date('Y-m-d H:i:s'),
                'center_id' => $center_id,
                'start_date' => $start_date,
                'end_date' => $end_date
            ];
            
            return $report;
            
        } catch (Exception $e) {
            error_log('Generate comprehensive report error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على التخطيط الشهري
     * Get monthly planning
     */
    public function getMonthlyPlanning($user_id, $item_type, $month, $year) {
        try {
            $sql = "SELECT * FROM monthly_planning
                    WHERE user_id = ? AND item_type = ? AND month_number = ? AND year = ?
                    ORDER BY created_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $item_type, $month, $year]);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log('Get monthly planning error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * حفظ التخطيط الشهري
     * Save monthly planning
     */
    public function saveMonthlyPlanning($user_id, $item_type, $item_id, $month, $year, $planned_quantity, $month_name = '') {
        try {
            $sql = "INSERT INTO monthly_planning
                    (user_id, item_type, item_id, month_number, year, planned_quantity, month_name)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    planned_quantity = VALUES(planned_quantity),
                    month_name = VALUES(month_name),
                    updated_at = CURRENT_TIMESTAMP";

            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id, $item_type, $item_id, $month, $year, $planned_quantity, $month_name]);

        } catch (Exception $e) {
            error_log('Save monthly planning error: ' . $e->getMessage());
            return false;
        }
    }
}
?>
