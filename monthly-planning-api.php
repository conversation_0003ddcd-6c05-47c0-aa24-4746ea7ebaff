<?php
/**
 * API مبسط للتخطيط الشهري للقاحات
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    if ($action === 'load_monthly_planning') {
        // إرجاع تخطيط افتراضي دائماً
        $defaultVaccines = [
            'HB1' => 0, 'BCG' => 0, 'VPO' => 0, 'VPI' => 0, 'Rota' => 0,
            'Penta' => 0, 'RR' => 0, 'Pneumo' => 0, 'DTC' => 0, 'VAT' => 0
        ];
        
        echo json_encode([
            'success' => true,
            'planning' => [
                'month1' => ['name' => '', 'vaccines' => $defaultVaccines],
                'month2' => ['name' => '', 'vaccines' => $defaultVaccines],
                'month3' => ['name' => '', 'vaccines' => $defaultVaccines]
            ],
            'is_default' => true,
            'message' => 'تم تحميل التخطيط الافتراضي'
        ], JSON_UNESCAPED_UNICODE);
        
    } elseif ($action === 'save_monthly_planning') {
        // محاكاة حفظ ناجح
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ التخطيط الشهري بنجاح'
        ], JSON_UNESCAPED_UNICODE);
        
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'إجراء غير معروف: ' . $action
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
