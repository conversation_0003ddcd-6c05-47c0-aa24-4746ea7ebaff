<?php
/**
 * فئة إدارة الرسائل
 * Message Management Class
 */

class MessageManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * إرسال رسالة
     * Send message
     */
    public function sendMessage($sender_id, $receiver_id, $message, $attachments = null) {
        try {
            $sql = "INSERT INTO messages (sender_id, receiver_id, message, attachments) 
                    VALUES (?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $sender_id,
                $receiver_id,
                $message,
                $attachments ? json_encode($attachments) : null
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Send message error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على المحادثات
     * Get conversations
     */
    public function getConversations($user_id) {
        try {
            $sql = "SELECT 
                        CASE 
                            WHEN m.sender_id = ? THEN m.receiver_id 
                            ELSE m.sender_id 
                        END as partner_id,
                        u.name as partner_name,
                        MAX(m.sent_at) as last_message_time,
                        COUNT(CASE WHEN m.receiver_id = ? AND m.is_read = 0 THEN 1 END) as unread_count,
                        (SELECT message FROM messages m2 
                         WHERE (m2.sender_id = ? AND m2.receiver_id = partner_id) 
                            OR (m2.sender_id = partner_id AND m2.receiver_id = ?)
                         ORDER BY m2.sent_at DESC LIMIT 1) as last_message
                    FROM messages m
                    JOIN users u ON u.id = CASE 
                        WHEN m.sender_id = ? THEN m.receiver_id 
                        ELSE m.sender_id 
                    END
                    WHERE m.sender_id = ? OR m.receiver_id = ?
                    GROUP BY partner_id, u.name
                    ORDER BY last_message_time DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $user_id, $user_id, $user_id, $user_id, $user_id, $user_id]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get conversations error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على رسائل المحادثة
     * Get conversation messages
     */
    public function getConversationMessages($user_id, $partner_id, $page = 1, $limit = 50) {
        try {
            $offset = ($page - 1) * $limit;
            
            $sql = "SELECT m.*, 
                           s.name as sender_name,
                           r.name as receiver_name
                    FROM messages m
                    JOIN users s ON m.sender_id = s.id
                    JOIN users r ON m.receiver_id = r.id
                    WHERE (m.sender_id = ? AND m.receiver_id = ?) 
                       OR (m.sender_id = ? AND m.receiver_id = ?)
                    ORDER BY m.sent_at DESC
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $partner_id, $partner_id, $user_id, $limit, $offset]);
            
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تحويل المرفقات من JSON
            foreach ($messages as &$message) {
                if ($message['attachments']) {
                    $message['attachments'] = json_decode($message['attachments'], true);
                }
            }
            
            return array_reverse($messages); // ترتيب تصاعدي للعرض
            
        } catch (Exception $e) {
            error_log('Get conversation messages error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تمييز الرسائل كمقروءة
     * Mark messages as read
     */
    public function markAsRead($user_id, $partner_id) {
        try {
            $sql = "UPDATE messages 
                    SET is_read = 1 
                    WHERE receiver_id = ? AND sender_id = ? AND is_read = 0";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id, $partner_id]);
            
        } catch (Exception $e) {
            error_log('Mark as read error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف رسالة
     * Delete message
     */
    public function deleteMessage($message_id, $user_id) {
        try {
            // التحقق من أن المستخدم هو المرسل
            $sql = "SELECT sender_id FROM messages WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$message_id]);
            $message = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$message || $message['sender_id'] !== $user_id) {
                throw new Exception('غير مصرح بحذف هذه الرسالة');
            }
            
            $sql = "DELETE FROM messages WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$message_id]);
            
        } catch (Exception $e) {
            error_log('Delete message error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على عدد الرسائل غير المقروءة
     * Get unread messages count
     */
    public function getUnreadCount($user_id) {
        try {
            $sql = "SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            error_log('Get unread count error: ' . $e->getMessage());
            return 0;
        }
    }
}

/**
 * فئة إدارة المهام
 * Task Management Class
 */

class TaskManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على مهام المستخدم
     * Get user tasks
     */
    public function getUserTasks($user_id, $status = null) {
        try {
            $sql = "SELECT * FROM tasks WHERE user_id = ?";
            $params = [$user_id];
            
            if ($status) {
                $sql .= " AND status = ?";
                $params[] = $status;
            }
            
            $sql .= " ORDER BY 
                        CASE 
                            WHEN priority = 'high' THEN 1
                            WHEN priority = 'medium' THEN 2
                            WHEN priority = 'low' THEN 3
                        END,
                        due_date ASC,
                        created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get user tasks error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إضافة مهمة جديدة
     * Add new task
     */
    public function addTask($user_id, $title, $description = null, $priority = 'medium', $due_date = null) {
        try {
            $sql = "INSERT INTO tasks (user_id, title, description, priority, due_date) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$user_id, $title, $description, $priority, $due_date]);
            
            if ($result) {
                return $this->db->lastInsertId();
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Add task error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث مهمة
     * Update task
     */
    public function updateTask($task_id, $user_id, $data) {
        try {
            // التحقق من ملكية المهمة
            $sql = "SELECT user_id FROM tasks WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$task_id]);
            $task = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$task || $task['user_id'] !== $user_id) {
                throw new Exception('غير مصرح بتعديل هذه المهمة');
            }
            
            // بناء استعلام التحديث
            $update_fields = [];
            $params = [];
            
            $allowed_fields = ['title', 'description', 'status', 'priority', 'due_date'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                throw new Exception('لا توجد بيانات للتحديث');
            }
            
            $update_fields[] = "updated_at = CURRENT_TIMESTAMP";
            $params[] = $task_id;
            
            $sql = "UPDATE tasks SET " . implode(', ', $update_fields) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
            
        } catch (Exception $e) {
            error_log('Update task error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف مهمة
     * Delete task
     */
    public function deleteTask($task_id, $user_id) {
        try {
            // التحقق من ملكية المهمة
            $sql = "SELECT user_id FROM tasks WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$task_id]);
            $task = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$task || $task['user_id'] !== $user_id) {
                throw new Exception('غير مصرح بحذف هذه المهمة');
            }
            
            $sql = "DELETE FROM tasks WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$task_id]);
            
        } catch (Exception $e) {
            error_log('Delete task error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على إحصائيات المهام
     * Get task statistics
     */
    public function getTaskStats($user_id) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_tasks,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
                        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_tasks,
                        SUM(CASE WHEN due_date < CURRENT_DATE() AND status NOT IN ('completed', 'cancelled') THEN 1 ELSE 0 END) as overdue_tasks
                    FROM tasks 
                    WHERE user_id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get task stats error: ' . $e->getMessage());
            return [];
        }
    }
}
?>
