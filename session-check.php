<?php
/**
 * فحص حالة الجلسة
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // فحص الجلسة
    if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
        // الاتصال بقاعدة البيانات للتحقق من المستخدم
        $host = '127.0.0.1';
        $dbname = 'csdb';
        $username = 'csdbuser';
        $password = 'j5aKN6lz5bsujTcWaYAd';
        
        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // البحث عن المستخدم
        $stmt = $pdo->prepare("
            SELECT u.*, c.name as center_name 
            FROM users u 
            LEFT JOIN centers c ON u.center_id = c.id 
            WHERE u.id = ? AND u.is_active = 1
            LIMIT 1
        ");
        
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo json_encode([
                'success' => true,
                'logged_in' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'name' => $user['name'],
                    'role' => $user['role'],
                    'center' => $user['center_name'] ?: 'المركز الصحي الرئيسي',
                    'center_id' => $user['center_id'],
                    'session_time' => $_SESSION['login_time'] ?? time()
                ]
            ], JSON_UNESCAPED_UNICODE);
        } else {
            // المستخدم غير موجود، مسح الجلسة
            session_destroy();
            echo json_encode([
                'success' => true,
                'logged_in' => false,
                'message' => 'المستخدم غير موجود'
            ], JSON_UNESCAPED_UNICODE);
        }
        
    } else {
        echo json_encode([
            'success' => true,
            'logged_in' => false,
            'message' => 'لا توجد جلسة نشطة'
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
