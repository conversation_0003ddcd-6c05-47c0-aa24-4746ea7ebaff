<?php
/**
 * API لإدارة المهام
 * Tasks Management API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';

try {
    // إنشاء اتصال قاعدة البيانات
    $database = new Database();
    $conn = $database->getConnection();
    
    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('بيانات غير صحيحة');
    }
    
    $action = $data['action'] ?? '';
    
    switch ($action) {
        case 'load':
            loadTasks($conn, $data);
            break;
            
        case 'save':
            saveTask($conn, $data);
            break;
            
        case 'update':
            updateTask($conn, $data);
            break;
            
        case 'delete':
            deleteTask($conn, $data);
            break;
            
        case 'toggle_completion':
            toggleTaskCompletion($conn, $data);
            break;
            
        default:
            throw new Exception('عملية غير مدعومة');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * تحميل مهام المستخدم
 */
function loadTasks($conn, $data) {
    $user_id = $data['user_id'] ?? '';
    
    if (empty($user_id)) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    $sql = "SELECT id, title, description, status, priority, due_date, 
                   created_at, updated_at,
                   CASE WHEN status = 'completed' THEN 1 ELSE 0 END as completed
            FROM tasks 
            WHERE user_id = ? 
            ORDER BY 
                CASE priority 
                    WHEN 'high' THEN 1 
                    WHEN 'medium' THEN 2 
                    WHEN 'low' THEN 3 
                END,
                due_date ASC,
                created_at DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$user_id]);
    $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحويل التواريخ للتنسيق المطلوب
    foreach ($tasks as &$task) {
        $task['completed'] = (bool)$task['completed'];
        if ($task['due_date']) {
            $task['due_date'] = date('Y-m-d', strtotime($task['due_date']));
        }
        $task['created_at'] = date('Y-m-d H:i:s', strtotime($task['created_at']));
        $task['updated_at'] = date('Y-m-d H:i:s', strtotime($task['updated_at']));
    }
    
    echo json_encode([
        'success' => true,
        'tasks' => $tasks,
        'count' => count($tasks)
    ]);
}

/**
 * حفظ مهمة جديدة
 */
function saveTask($conn, $data) {
    $user_id = $data['user_id'] ?? '';
    $title = trim($data['title'] ?? '');
    $description = trim($data['description'] ?? '');
    $priority = $data['priority'] ?? 'medium';
    $due_date = $data['due_date'] ?? null;
    
    if (empty($user_id) || empty($title)) {
        throw new Exception('معرف المستخدم وعنوان المهمة مطلوبان');
    }
    
    // التحقق من صحة الأولوية
    $valid_priorities = ['low', 'medium', 'high'];
    if (!in_array($priority, $valid_priorities)) {
        $priority = 'medium';
    }
    
    // التحقق من صحة التاريخ
    if ($due_date && !strtotime($due_date)) {
        $due_date = null;
    }
    
    $sql = "INSERT INTO tasks (user_id, title, description, priority, due_date, status) 
            VALUES (?, ?, ?, ?, ?, 'pending')";
    
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$user_id, $title, $description, $priority, $due_date]);
    
    if ($result) {
        $task_id = $conn->lastInsertId();
        
        // إرجاع بيانات المهمة الجديدة
        $sql = "SELECT id, title, description, status, priority, due_date, 
                       created_at, updated_at,
                       CASE WHEN status = 'completed' THEN 1 ELSE 0 END as completed
                FROM tasks WHERE id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([$task_id]);
        $task = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($task) {
            $task['completed'] = (bool)$task['completed'];
            if ($task['due_date']) {
                $task['due_date'] = date('Y-m-d', strtotime($task['due_date']));
            }
            $task['created_at'] = date('Y-m-d H:i:s', strtotime($task['created_at']));
            $task['updated_at'] = date('Y-m-d H:i:s', strtotime($task['updated_at']));
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة المهمة بنجاح',
            'task' => $task
        ]);
    } else {
        throw new Exception('فشل في إضافة المهمة');
    }
}

/**
 * تحديث مهمة موجودة
 */
function updateTask($conn, $data) {
    $task_id = $data['task_id'] ?? '';
    $title = trim($data['title'] ?? '');
    $description = trim($data['description'] ?? '');
    $priority = $data['priority'] ?? 'medium';
    $due_date = $data['due_date'] ?? null;
    
    if (empty($task_id) || empty($title)) {
        throw new Exception('معرف المهمة وعنوان المهمة مطلوبان');
    }
    
    // التحقق من صحة الأولوية
    $valid_priorities = ['low', 'medium', 'high'];
    if (!in_array($priority, $valid_priorities)) {
        $priority = 'medium';
    }
    
    // التحقق من صحة التاريخ
    if ($due_date && !strtotime($due_date)) {
        $due_date = null;
    }
    
    $sql = "UPDATE tasks 
            SET title = ?, description = ?, priority = ?, due_date = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$title, $description, $priority, $due_date, $task_id]);
    
    if ($result) {
        // إرجاع بيانات المهمة المحدثة
        $sql = "SELECT id, title, description, status, priority, due_date, 
                       created_at, updated_at,
                       CASE WHEN status = 'completed' THEN 1 ELSE 0 END as completed
                FROM tasks WHERE id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([$task_id]);
        $task = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($task) {
            $task['completed'] = (bool)$task['completed'];
            if ($task['due_date']) {
                $task['due_date'] = date('Y-m-d', strtotime($task['due_date']));
            }
            $task['created_at'] = date('Y-m-d H:i:s', strtotime($task['created_at']));
            $task['updated_at'] = date('Y-m-d H:i:s', strtotime($task['updated_at']));
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث المهمة بنجاح',
            'task' => $task
        ]);
    } else {
        throw new Exception('فشل في تحديث المهمة');
    }
}

/**
 * حذف مهمة
 */
function deleteTask($conn, $data) {
    $task_id = $data['task_id'] ?? '';
    
    if (empty($task_id)) {
        throw new Exception('معرف المهمة مطلوب');
    }
    
    $sql = "DELETE FROM tasks WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$task_id]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف المهمة بنجاح'
        ]);
    } else {
        throw new Exception('فشل في حذف المهمة');
    }
}

/**
 * تغيير حالة إنجاز المهمة
 */
function toggleTaskCompletion($conn, $data) {
    $task_id = $data['task_id'] ?? '';
    $completed = $data['completed'] ?? false;
    
    if (empty($task_id)) {
        throw new Exception('معرف المهمة مطلوب');
    }
    
    $status = $completed ? 'completed' : 'pending';
    
    $sql = "UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$status, $task_id]);
    
    if ($result) {
        // إرجاع بيانات المهمة المحدثة
        $sql = "SELECT id, title, description, status, priority, due_date, 
                       created_at, updated_at,
                       CASE WHEN status = 'completed' THEN 1 ELSE 0 END as completed
                FROM tasks WHERE id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([$task_id]);
        $task = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($task) {
            $task['completed'] = (bool)$task['completed'];
            if ($task['due_date']) {
                $task['due_date'] = date('Y-m-d', strtotime($task['due_date']));
            }
            $task['created_at'] = date('Y-m-d H:i:s', strtotime($task['created_at']));
            $task['updated_at'] = date('Y-m-d H:i:s', strtotime($task['updated_at']));
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث حالة المهمة بنجاح',
            'task' => $task
        ]);
    } else {
        throw new Exception('فشل في تحديث حالة المهمة');
    }
}
?>
