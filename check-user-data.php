<?php
/**
 * فحص بيانات المستخدم الحالي
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // جلب جميع المستخدمين مع بيانات المراكز
    $stmt = $pdo->query("
        SELECT u.id, u.username, u.name, u.role, u.center_id, 
               c.name as center_name, c.location as center_location
        FROM users u 
        LEFT JOIN centers c ON u.center_id = c.id 
        WHERE u.is_active = 1 
        ORDER BY u.center_id, u.name
    ");
    $users = $stmt->fetchAll();
    
    // تجميع المستخدمين حسب المركز
    $usersByCenter = [];
    foreach ($users as $user) {
        $center_id = $user['center_id'] ?? 'no_center';
        if (!isset($usersByCenter[$center_id])) {
            $usersByCenter[$center_id] = [
                'center_info' => [
                    'id' => $user['center_id'],
                    'name' => $user['center_name'],
                    'location' => $user['center_location']
                ],
                'users' => []
            ];
        }
        $usersByCenter[$center_id]['users'][] = $user;
    }
    
    echo json_encode([
        'success' => true,
        'total_users' => count($users),
        'users_by_center' => $usersByCenter,
        'all_users' => $users
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
