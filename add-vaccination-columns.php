<?php
/**
 * إضافة أعمدة مواعيد التلقيح إلى جدول الأطفال
 */

require_once 'config/database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "🔄 بدء تحديث جدول الأطفال...\n\n";
    
    // فحص الأعمدة الموجودة
    $stmt = $conn->query("SHOW COLUMNS FROM children");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');
    
    echo "📋 الأعمدة الموجودة حالياً:\n";
    foreach ($columnNames as $column) {
        echo "- $column\n";
    }
    echo "\n";
    
    $needsUpdate = false;
    
    // فحص وإضافة عمود vaccination_dates
    if (!in_array('vaccination_dates', $columnNames)) {
        echo "➕ إضافة عمود vaccination_dates...\n";
        $conn->exec("ALTER TABLE children ADD COLUMN vaccination_dates TEXT");
        echo "✅ تم إضافة عمود vaccination_dates\n";
        $needsUpdate = true;
    } else {
        echo "✅ عمود vaccination_dates موجود بالفعل\n";
    }
    
    // فحص وإضافة عمود completed_vaccinations
    if (!in_array('completed_vaccinations', $columnNames)) {
        echo "➕ إضافة عمود completed_vaccinations...\n";
        $conn->exec("ALTER TABLE children ADD COLUMN completed_vaccinations TEXT");
        echo "✅ تم إضافة عمود completed_vaccinations\n";
        $needsUpdate = true;
    } else {
        echo "✅ عمود completed_vaccinations موجود بالفعل\n";
    }
    
    if ($needsUpdate) {
        echo "\n🎉 تم تحديث جدول الأطفال بنجاح!\n";
        
        // عرض البنية الجديدة
        echo "\n📋 البنية الجديدة للجدول:\n";
        $stmt = $conn->query("SHOW COLUMNS FROM children");
        $newColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($newColumns as $column) {
            echo "- {$column['Field']} ({$column['Type']})\n";
        }
    } else {
        echo "\n✅ الجدول محدث بالفعل - لا حاجة لتغييرات\n";
    }
    
    // فحص عدد الأطفال
    $stmt = $conn->query("SELECT COUNT(*) as count FROM children");
    $count = $stmt->fetch()['count'];
    echo "\n📊 عدد الأطفال في الجدول: $count\n";
    
    if ($count > 0) {
        echo "\n👶 الأطفال الموجودون:\n";
        $stmt = $conn->query("SELECT id, name, birth_date, nurse_id, center_id FROM children ORDER BY created_at DESC");
        $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($children as $child) {
            echo "- {$child['name']} (ID: {$child['id']}) - {$child['birth_date']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
