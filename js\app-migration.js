/**
 * ملف ترحيل التطبيق من localStorage إلى API
 * Application Migration from localStorage to API
 */

// عميل API متاح تلقائياً من ملف api-client.js

// متغيرات عامة
let currentUser = null;
let childrenDatabase = [];
let vaccineList = [];
let medicineList = [];

/**
 * تهيئة التطبيق
 * Initialize application
 */
async function initializeApp() {
    try {
        // التحقق من وجود عميل API
        if (typeof apiClient === 'undefined') {
            console.error('عميل API غير متاح');
            showLoginForm();
            return;
        }

        currentUser = await apiClient.checkAuth();

        if (currentUser) {
            showMainInterface();
            await loadAllData();
        } else {
            showLoginForm();
        }
    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        showLoginForm();
    }
}

/**
 * عرض نموذج تسجيل الدخول
 * Show login form
 */
function showLoginForm() {
    const loginForm = document.getElementById('loginForm');
    const mainPage = document.getElementById('mainPage');
    
    if (loginForm) loginForm.style.display = 'block';
    if (mainPage) mainPage.style.display = 'none';
}

/**
 * عرض الواجهة الرئيسية
 * Show main interface
 */
function showMainInterface() {
    const loginForm = document.getElementById('loginForm');
    const mainPage = document.getElementById('mainPage');
    
    if (loginForm) loginForm.style.display = 'none';
    if (mainPage) mainPage.style.display = 'block';
}

/**
 * تحميل جميع البيانات
 * Load all data
 */
async function loadAllData() {
    try {
        // تحميل البيانات بشكل متوازي
        const [children, vaccines, medicines, contraceptives] = await Promise.all([
            apiClient.getChildren(1, 1000), // تحميل جميع الأطفال
            apiClient.getVaccines(),
            apiClient.getMedicines(),
            apiClient.getAllContraceptives()
        ]);

        childrenDatabase = children.children || [];
        vaccineList = vaccines || [];
        medicineList = medicines || [];
        contraceptiveList = contraceptives || [];

        // تحديث الواجهة
        updateDashboard();
        displayChildrenList();
        
        console.log('تم تحميل جميع البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        apiClient.showError('فشل في تحميل البيانات');
    }
}

/**
 * تسجيل الدخول
 * Login function
 */
async function login() {
    const username = document.getElementById('username')?.value;
    const password = document.getElementById('password')?.value;

    if (!username || !password) {
        showError('يرجى إدخال اسم المستخدم وكلمة المرور');
        return;
    }

    try {
        // استخدام direct-login.php مباشرة
        const response = await fetch('direct-login.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const data = await response.json();

        if (data.success) {
            currentUser = data.data;
            showMainInterface();
            await loadAllData();
        } else {
            showError('خطأ في تسجيل الدخول: ' + data.message);
        }

    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        showError('خطأ في تسجيل الدخول: ' + error.message);
    }
}

/**
 * تسجيل الخروج
 * Logout function
 */
async function logout() {
    try {
        await apiClient.logout();
        currentUser = null;
        showLoginForm();
    } catch (error) {
        console.error('خطأ في تسجيل الخروج:', error);
    }
}

/**
 * إضافة طفل جديد
 * Add new child
 */
async function addChild() {
    const name = document.getElementById('childName')?.value;
    const birthDate = document.getElementById('birthDate')?.value;
    const gender = document.getElementById('childGender')?.value;
    const parentName = document.getElementById('parentName')?.value;
    const parentPhone = document.getElementById('parentPhone')?.value;
    const address = document.getElementById('childAddress')?.value;

    if (!name || !birthDate) {
        apiClient.showError('يرجى إدخال اسم الطفل وتاريخ الميلاد');
        return;
    }

    try {
        const childData = {
            name,
            birth_date: birthDate,
            gender,
            parent_name: parentName,
            parent_phone: parentPhone,
            address,
            nurse_id: currentUser.id,
            center_id: currentUser.center_id
        };

        const newChild = await apiClient.addChild(childData);
        
        // إضافة الطفل للقائمة المحلية
        childrenDatabase.unshift(newChild);
        
        // تحديث الواجهة
        displayChildrenList();
        updateDashboard();
        
        // مسح النموذج
        clearChildForm();
        
        apiClient.showSuccess('تم إضافة الطفل بنجاح');
    } catch (error) {
        console.error('خطأ في إضافة الطفل:', error);
    }
}

/**
 * تحديث بيانات طفل
 * Update child data
 */
async function updateChild(childId, childData) {
    try {
        const updatedChild = await apiClient.updateChild(childId, childData);
        
        // تحديث القائمة المحلية
        const index = childrenDatabase.findIndex(child => child.id === childId);
        if (index !== -1) {
            childrenDatabase[index] = updatedChild;
        }
        
        // تحديث الواجهة
        displayChildrenList();
        
        apiClient.showSuccess('تم تحديث بيانات الطفل بنجاح');
        return updatedChild;
    } catch (error) {
        console.error('خطأ في تحديث بيانات الطفل:', error);
        throw error;
    }
}

/**
 * حذف طفل
 * Delete child
 */
async function deleteChild(childId) {
    if (!confirm('هل أنت متأكد من حذف هذا الطفل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    try {
        await apiClient.deleteChild(childId);
        
        // إزالة الطفل من القائمة المحلية
        childrenDatabase = childrenDatabase.filter(child => child.id !== childId);
        
        // تحديث الواجهة
        displayChildrenList();
        updateDashboard();
        
        apiClient.showSuccess('تم حذف الطفل بنجاح');
    } catch (error) {
        console.error('خطأ في حذف الطفل:', error);
    }
}

/**
 * البحث عن الأطفال
 * Search children
 */
async function searchChildren(searchTerm) {
    try {
        const result = await apiClient.getChildren(1, 100, searchTerm);
        childrenDatabase = result.children || [];
        displayChildrenList();
    } catch (error) {
        console.error('خطأ في البحث:', error);
    }
}

/**
 * تحديث لوحة المعلومات
 * Update dashboard
 */
async function updateDashboard() {
    try {
        const stats = await apiClient.getStats();
        
        // تحديث الإحصائيات في الواجهة
        const totalChildrenElement = document.getElementById('totalChildren');
        const completedVaccinationsElement = document.getElementById('completedVaccinations');
        const monthlyRegistrationsElement = document.getElementById('monthlyRegistrations');

        if (totalChildrenElement) {
            totalChildrenElement.textContent = stats.total_children || 0;
        }
        
        if (completedVaccinationsElement) {
            completedVaccinationsElement.textContent = stats.completed_vaccinations || 0;
        }
        
        if (monthlyRegistrationsElement) {
            monthlyRegistrationsElement.textContent = stats.monthly_registrations || 0;
        }

    } catch (error) {
        console.error('خطأ في تحديث لوحة المعلومات:', error);
    }
}

/**
 * عرض قائمة الأطفال
 * Display children list
 */
function displayChildrenList() {
    const childrenListElement = document.getElementById('childrenList');
    if (!childrenListElement) return;

    if (childrenDatabase.length === 0) {
        childrenListElement.innerHTML = '<p class="no-data">لا توجد بيانات أطفال</p>';
        return;
    }

    let html = '';
    childrenDatabase.forEach(child => {
        const age = calculateAge(child.birth_date);
        const completedVaccinations = child.vaccinations ? 
            child.vaccinations.filter(v => v.is_completed).length : 0;
        const totalVaccinations = child.vaccinations ? child.vaccinations.length : 0;
        const completionRate = totalVaccinations > 0 ? 
            Math.round((completedVaccinations / totalVaccinations) * 100) : 0;

        html += `
            <div class="child-card" data-child-id="${child.id}">
                <div class="child-header">
                    <h3>${child.name}</h3>
                    <div class="child-actions">
                        <button onclick="editChild('${child.id}')" class="btn-edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteChild('${child.id}')" class="btn-delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="child-info">
                    <p><strong>تاريخ الميلاد:</strong> ${child.birth_date}</p>
                    <p><strong>العمر:</strong> ${age}</p>
                    ${child.parent_name ? `<p><strong>ولي الأمر:</strong> ${child.parent_name}</p>` : ''}
                    ${child.parent_phone ? `<p><strong>الهاتف:</strong> ${child.parent_phone}</p>` : ''}
                </div>
                <div class="vaccination-progress">
                    <div class="progress-info">
                        <span>التلقيحات: ${completedVaccinations}/${totalVaccinations}</span>
                        <span>${completionRate}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${completionRate}%"></div>
                    </div>
                </div>
            </div>
        `;
    });

    childrenListElement.innerHTML = html;
}

/**
 * حساب العمر
 * Calculate age
 */
function calculateAge(birthDate) {
    const birth = new Date(birthDate);
    const now = new Date();
    const ageInMonths = (now.getFullYear() - birth.getFullYear()) * 12 + 
                       (now.getMonth() - birth.getMonth());
    
    if (ageInMonths < 12) {
        return `${ageInMonths} شهر`;
    } else {
        const years = Math.floor(ageInMonths / 12);
        const months = ageInMonths % 12;
        return months > 0 ? `${years} سنة و ${months} شهر` : `${years} سنة`;
    }
}

/**
 * مسح نموذج الطفل
 * Clear child form
 */
function clearChildForm() {
    const form = document.getElementById('childForm');
    if (form) {
        form.reset();
    }
}

/**
 * تحديث مخزون اللقاحات
 * Update vaccine stock
 */
async function updateVaccineStock(vaccineId, quantity, expiryDate = null, batchNumber = null) {
    try {
        await apiClient.updateVaccineStock(vaccineId, quantity, expiryDate, batchNumber);
        apiClient.showSuccess('تم تحديث مخزون اللقاح بنجاح');
        
        // إعادة تحميل بيانات المخزون
        await loadVaccineStock();
    } catch (error) {
        console.error('خطأ في تحديث مخزون اللقاح:', error);
    }
}

/**
 * تحميل مخزون اللقاحات
 * Load vaccine stock
 */
async function loadVaccineStock() {
    try {
        const stock = await apiClient.getVaccineStock();
        displayVaccineStock(stock);
    } catch (error) {
        console.error('خطأ في تحميل مخزون اللقاحات:', error);
    }
}

/**
 * عرض مخزون اللقاحات
 * Display vaccine stock
 */
function displayVaccineStock(stock) {
    const stockElement = document.getElementById('vaccineStock');
    if (!stockElement) return;

    if (!stock || stock.length === 0) {
        stockElement.innerHTML = '<p class="no-data">لا توجد بيانات مخزون</p>';
        return;
    }

    let html = '<div class="stock-grid">';
    stock.forEach(item => {
        const isLowStock = item.quantity <= 10;
        const isExpired = item.expiry_date && new Date(item.expiry_date) < new Date();
        
        html += `
            <div class="stock-item ${isLowStock ? 'low-stock' : ''} ${isExpired ? 'expired' : ''}">
                <h4>${item.name_ar}</h4>
                <p class="quantity">الكمية: ${item.quantity}</p>
                ${item.expiry_date ? `<p class="expiry">انتهاء الصلاحية: ${item.expiry_date}</p>` : ''}
                ${item.batch_number ? `<p class="batch">رقم الدفعة: ${item.batch_number}</p>` : ''}
                <button onclick="editVaccineStock('${item.vaccine_id}')" class="btn-edit-stock">
                    تعديل المخزون
                </button>
            </div>
        `;
    });
    html += '</div>';

    stockElement.innerHTML = html;
}

// ربط الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    
    // ربط نموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            login();
        });
    }
    
    // ربط نموذج إضافة الطفل
    const childForm = document.getElementById('childForm');
    if (childForm) {
        childForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addChild();
        });
    }
    
    // ربط مربع البحث
    const searchBox = document.getElementById('searchBox');
    if (searchBox) {
        searchBox.addEventListener('input', function(e) {
            const searchTerm = e.target.value.trim();
            if (searchTerm.length >= 2 || searchTerm.length === 0) {
                searchChildren(searchTerm);
            }
        });
    }
});

// تصدير الوظائف للاستخدام العام
window.login = login;
window.logout = logout;
window.addChild = addChild;
window.updateChild = updateChild;
window.deleteChild = deleteChild;
window.searchChildren = searchChildren;
window.updateVaccineStock = updateVaccineStock;
