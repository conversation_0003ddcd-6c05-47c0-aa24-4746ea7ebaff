# دليل البدء السريع - استعادة قاعدة البيانات

## 🚀 الطريقة السريعة (باستخدام السكريبت التلقائي)

### 1. تشغيل السكريبت التلقائي:
```bash
# منح صلاحية التنفيذ
chmod +x restore_database.sh

# تشغيل السكريبت بالإعدادات الافتراضية
./restore_database.sh

# أو مع إنشاء نسخة احتياطية
./restore_database.sh -b

# أو فحص الاتصال فقط
./restore_database.sh -c
```

### 2. مع معاملات مخصصة:
```bash
./restore_database.sh -h 127.0.0.1 -d csdb -u csdbuser -p your_password
```

---

## 🖥️ الطريقة السريعة عبر CloudPanel

### 1. <PERSON><PERSON>ر phpMyAdmin (الأسهل):
1. في CloudPanel → Database Users → انقر "Manage" بجانب `csdbuser`
2. في phpMyAdmin → اختر قاعدة البيانات `csdb`
3. انقر "Import" → اختر `database_complete_rebuild.sql`
4. تأكد من Character set: utf8mb4 → انقر "Go"

### 2. عبر Terminal:
```bash
mysql -h 127.0.0.1 -u csdbuser -p csdb < database_complete_rebuild.sql
```

---

## ⚡ الطريقة اليدوية السريعة

### 1. إنشاء قاعدة البيانات (إذا لم تكن موجودة):
```sql
mysql -h 127.0.0.1 -u csdbuser -p -e "CREATE DATABASE IF NOT EXISTS csdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 2. استيراد البيانات:
```bash
mysql -h 127.0.0.1 -u csdbuser -p csdb < database_complete_rebuild.sql
```

### 3. التحقق من النجاح:
```sql
mysql -h 127.0.0.1 -u csdbuser -p csdb -e "SHOW TABLES; SELECT COUNT(*) FROM users;"
```

---

## 🔧 إعدادات قاعدة البيانات (CloudPanel)

- **Host**: `127.0.0.1`
- **Port**: `3306`
- **اسم قاعدة البيانات**: `csdb`
- **المستخدم**: `csdbuser`
- **كلمة المرور**: `j5aKN6lz5bsujTcWaYAd`
- **الترميز**: `utf8mb4_unicode_ci`

---

## 📋 التحقق من النجاح

بعد الاستعادة، تحقق من:
- عدد الجداول: 22 جدول
- عدد المستخدمين: 4 مستخدمين
- عدد اللقاحات: 19 لقاح
- عدد المراكز: 3 مراكز

---

## 🆘 في حالة المشاكل

1. **خطأ في الاتصال**: تحقق من معاملات الاتصال
2. **خطأ في الصلاحيات**: امنح صلاحيات إضافية للمستخدم
3. **خطأ في الترميز**: تأكد من دعم utf8mb4
4. **راجع الملفات**:
   - `database_restoration_guide.md` - دليل مفصل
   - `import_errors.log` - سجل الأخطاء
   - `database_analysis_report.md` - تقرير التحليل

---

## 📞 الدعم

للحصول على مساعدة مفصلة، راجع:
- `CloudPanel_Restoration_Guide.md` - دليل CloudPanel المخصص
- `database_restoration_guide.md` - الدليل الشامل
- `database_analysis_report.md` - تقرير التحليل الكامل

---

## ✅ قائمة مراجعة سريعة

- [ ] تم تشغيل السكريبت بنجاح
- [ ] تم إنشاء 22 جدول
- [ ] تم استيراد البيانات الافتراضية
- [ ] تم اختبار الاتصال من التطبيق
- [ ] تم إعداد النسخ الاحتياطي التلقائي
