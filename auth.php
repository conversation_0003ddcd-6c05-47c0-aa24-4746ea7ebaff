<?php
/**
 * ملف المصادقة لنظام إدارة المراكز الصحية
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // قراءة البيانات
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $user_username = $input['username'] ?? '';
    $user_password = $input['password'] ?? '';
    
    if (empty($user_username) || empty($user_password)) {
        throw new Exception('يرجى إدخال اسم المستخدم وكلمة المرور');
    }
    
    // البحث عن المستخدم
    $stmt = $pdo->prepare("
        SELECT u.*, c.name as center_name 
        FROM users u 
        LEFT JOIN centers c ON u.center_id = c.id 
        WHERE u.username = ? AND u.is_active = 1
        LIMIT 1
    ");
    
    $stmt->execute([$user_username]);
    $user = $stmt->fetch();
    
    if (!$user) {
        throw new Exception('اسم المستخدم غير موجود');
    }
    
    // التحقق من كلمة المرور
    if (!password_verify($user_password, $user['password'])) {
        throw new Exception('كلمة المرور غير صحيحة');
    }
    
    // إنشاء الجلسة
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['center_id'] = $user['center_id'];
    $_SESSION['login_time'] = time();
    
    // إعداد الاستجابة
    echo json_encode([
        'success' => true,
        'message' => 'تم تسجيل الدخول بنجاح',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'name' => $user['name'],
            'role' => $user['role'],
            'center' => $user['center'] ?: ($user['center_name'] ?: 'المركز الصحي الرئيسي'),
            'region' => $user['region'] ?: 'المنطقة',
            'center_id' => $user['center_id']
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
