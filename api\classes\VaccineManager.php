<?php
/**
 * فئة إدارة اللقاحات
 * Vaccine Management Class
 */

class VaccineManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على جميع اللقاحات
     * Get all vaccines
     */
    public function getAllVaccines() {
        try {
            $sql = "SELECT * FROM vaccines WHERE is_active = 1 ORDER BY age_months";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get all vaccines error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مخزون اللقاحات للمستخدم
     * Get vaccine stock for user
     */
    public function getVaccineStock($user_id) {
        try {
            $sql = "SELECT vs.*, v.name_ar, v.name_fr 
                    FROM vaccine_stock vs
                    JOIN vaccines v ON vs.vaccine_id = v.id
                    WHERE vs.user_id = ?
                    ORDER BY v.age_months";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get vaccine stock error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث مخزون لقاح
     * Update vaccine stock
     */
    public function updateVaccineStock($user_id, $vaccine_id, $quantity, $expiry_date = null, $batch_number = null) {
        try {
            $sql = "INSERT INTO vaccine_stock (vaccine_id, user_id, quantity, expiry_date, batch_number)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    quantity = VALUES(quantity),
                    expiry_date = VALUES(expiry_date),
                    batch_number = VALUES(batch_number),
                    updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$vaccine_id, $user_id, $quantity, $expiry_date, $batch_number]);
            
        } catch (Exception $e) {
            error_log('Update vaccine stock error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على التخطيط الشهري للقاحات
     * Get monthly vaccine planning
     */
    public function getMonthlyPlanning($user_id, $year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }
            
            $sql = "SELECT mp.*, v.name_ar, v.name_fr
                    FROM monthly_planning mp
                    JOIN vaccines v ON mp.item_id = v.id
                    WHERE mp.user_id = ? AND mp.item_type = 'vaccine' AND mp.year = ?
                    ORDER BY mp.month_number, v.age_months";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $year]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get monthly planning error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث التخطيط الشهري
     * Update monthly planning
     */
    public function updateMonthlyPlanning($user_id, $month_number, $month_name, $vaccine_id, $planned_quantity, $year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }
            
            $sql = "INSERT INTO monthly_planning (user_id, month_number, month_name, item_type, item_id, planned_quantity, year)
                    VALUES (?, ?, ?, 'vaccine', ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    month_name = VALUES(month_name),
                    planned_quantity = VALUES(planned_quantity),
                    updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id, $month_number, $month_name, $vaccine_id, $planned_quantity, $year]);
            
        } catch (Exception $e) {
            error_log('Update monthly planning error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على سجل استخدام اللقاحات
     * Get vaccine usage log
     */
    public function getUsageLog($user_id, $start_date = null, $end_date = null) {
        try {
            $sql = "SELECT vul.*, v.name_ar, v.name_fr, c.name as child_name
                    FROM vaccine_usage_log vul
                    JOIN vaccines v ON vul.vaccine_id = v.id
                    LEFT JOIN children c ON vul.child_id = c.id
                    WHERE vul.user_id = ?";
            
            $params = [$user_id];
            
            if ($start_date) {
                $sql .= " AND vul.usage_date >= ?";
                $params[] = $start_date;
            }
            
            if ($end_date) {
                $sql .= " AND vul.usage_date <= ?";
                $params[] = $end_date;
            }
            
            $sql .= " ORDER BY vul.usage_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get usage log error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إضافة لقاح جديد
     * Add new vaccine
     */
    public function addVaccine($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['id', 'name_ar', 'age_months'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("الحقل {$field} مطلوب");
                }
            }
            
            // التحقق من عدم تكرار المعرف
            if ($this->vaccineExists($data['id'])) {
                throw new Exception('معرف اللقاح موجود مسبقاً');
            }
            
            $sql = "INSERT INTO vaccines (id, name_ar, name_fr, description, age_months, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $data['id'],
                $data['name_ar'],
                $data['name_fr'] ?? null,
                $data['description'] ?? null,
                $data['age_months'],
                $data['is_active'] ?? 1
            ]);
            
        } catch (Exception $e) {
            error_log('Add vaccine error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث لقاح
     * Update vaccine
     */
    public function updateVaccine($id, $data) {
        try {
            // التحقق من وجود اللقاح
            if (!$this->vaccineExists($id)) {
                throw new Exception('اللقاح غير موجود');
            }
            
            // بناء استعلام التحديث
            $update_fields = [];
            $params = [];
            
            $allowed_fields = ['name_ar', 'name_fr', 'description', 'age_months', 'is_active'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                throw new Exception('لا توجد بيانات للتحديث');
            }
            
            $params[] = $id;
            
            $sql = "UPDATE vaccines SET " . implode(', ', $update_fields) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
            
        } catch (Exception $e) {
            error_log('Update vaccine error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف لقاح
     * Delete vaccine
     */
    public function deleteVaccine($id) {
        try {
            // التحقق من وجود اللقاح
            if (!$this->vaccineExists($id)) {
                throw new Exception('اللقاح غير موجود');
            }
            
            // التحقق من عدم وجود تلقيحات مرتبطة
            $sql = "SELECT COUNT(*) FROM child_vaccinations WHERE vaccine_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف اللقاح لوجود تلقيحات مرتبطة به');
            }
            
            // حذف اللقاح
            $sql = "DELETE FROM vaccines WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$id]);
            
        } catch (Exception $e) {
            error_log('Delete vaccine error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * التحقق من وجود اللقاح
     * Check if vaccine exists
     */
    private function vaccineExists($id) {
        try {
            $sql = "SELECT COUNT(*) FROM vaccines WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log('Vaccine exists check error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات اللقاحات
     * Get vaccine statistics
     */
    public function getVaccineStats($user_id = null, $center_id = null) {
        try {
            $stats = [];
            
            // إجمالي اللقاحات المتاحة
            $sql = "SELECT COUNT(*) FROM vaccines WHERE is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['total_vaccines'] = $stmt->fetchColumn();
            
            // إجمالي المخزون
            $sql = "SELECT SUM(quantity) FROM vaccine_stock";
            $params = [];
            
            if ($user_id) {
                $sql .= " WHERE user_id = ?";
                $params[] = $user_id;
            } elseif ($center_id) {
                $sql .= " WHERE user_id IN (SELECT id FROM users WHERE center_id = ?)";
                $params[] = $center_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['total_stock'] = $stmt->fetchColumn() ?: 0;
            
            // اللقاحات المستخدمة هذا الشهر
            $sql = "SELECT SUM(quantity_used) FROM vaccine_usage_log 
                    WHERE MONTH(usage_date) = MONTH(CURRENT_DATE()) 
                    AND YEAR(usage_date) = YEAR(CURRENT_DATE())";
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " AND user_id IN (SELECT id FROM users WHERE center_id = ?)";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['monthly_usage'] = $stmt->fetchColumn() ?: 0;
            
            // اللقاحات منتهية الصلاحية
            $sql = "SELECT COUNT(*) FROM vaccine_stock 
                    WHERE expiry_date IS NOT NULL AND expiry_date < CURRENT_DATE()";
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " AND user_id IN (SELECT id FROM users WHERE center_id = ?)";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['expired_vaccines'] = $stmt->fetchColumn() ?: 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Get vaccine stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على اللقاحات منتهية الصلاحية
     * Get expired vaccines
     */
    public function getExpiredVaccines($user_id = null) {
        try {
            $sql = "SELECT vs.*, v.name_ar, v.name_fr, u.name as user_name
                    FROM vaccine_stock vs
                    JOIN vaccines v ON vs.vaccine_id = v.id
                    JOIN users u ON vs.user_id = u.id
                    WHERE vs.expiry_date IS NOT NULL AND vs.expiry_date < CURRENT_DATE()";
            
            $params = [];
            
            if ($user_id) {
                $sql .= " AND vs.user_id = ?";
                $params[] = $user_id;
            }
            
            $sql .= " ORDER BY vs.expiry_date";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get expired vaccines error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على اللقاحات التي ستنتهي صلاحيتها قريباً
     * Get vaccines expiring soon
     */
    public function getVaccinesExpiringSoon($user_id = null, $days = 30) {
        try {
            $sql = "SELECT vs.*, v.name_ar, v.name_fr, u.name as user_name
                    FROM vaccine_stock vs
                    JOIN vaccines v ON vs.vaccine_id = v.id
                    JOIN users u ON vs.user_id = u.id
                    WHERE vs.expiry_date IS NOT NULL 
                    AND vs.expiry_date BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL ? DAY)";
            
            $params = [$days];
            
            if ($user_id) {
                $sql .= " AND vs.user_id = ?";
                $params[] = $user_id;
            }
            
            $sql .= " ORDER BY vs.expiry_date";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get vaccines expiring soon error: ' . $e->getMessage());
            return [];
        }
    }
}
?>
