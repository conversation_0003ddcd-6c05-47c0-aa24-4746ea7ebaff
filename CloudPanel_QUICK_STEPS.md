# خطوات سريعة لاستعادة قاعدة البيانات عبر CloudPanel

## 🎯 الهدف
استعادة قاعدة البيانات `csdb` باستخدام CloudPanel على Hostinger

## ✅ المتطلبات المتوفرة
- ✅ قاعدة البيانات: `csdb` (تم إنشاؤها)
- ✅ المستخدم: `csdbuser` (تم إنشاؤه)
- ✅ الصلاحيات: Read and Write (تم تعيينها)
- ✅ Host: `127.0.0.1`
- ✅ Port: `3306`

## 🚀 الطريقة الأسرع: phpMyAdmin

### الخطوات:
1. **في CloudPanel**:
   - اذهب إلى "Database Users"
   - ابحث عن `csdbuser`
   - انقر "Manage"

2. **في phpMyAdmin**:
   - اختر قاعدة البيانات `csdb` من القائمة اليسرى
   - انقر تبويب "Import"
   - اختر ملف `database_complete_rebuild.sql`
   - تأكد من Character set: `utf8mb4`
   - انقر "Go"

3. **انتظار الانتهاء**:
   - مراقبة شريط التقدم
   - انتظار رسالة "Import has been successfully finished"

## 🔍 التحقق السريع

### في phpMyAdmin:
1. اختر قاعدة البيانات `csdb`
2. تحقق من وجود **22 جدول**
3. افتح جدول `users` → يجب أن يحتوي على **4 مستخدمين**
4. افتح جدول `vaccines` → يجب أن يحتوي على **19 لقاح**

### النتائج المتوقعة:
- **الجداول**: 22 جدول
- **المستخدمين**: 4 (admin, nurse1, nurse2, supervisor1)
- **اللقاحات**: 19 لقاح
- **المراكز**: 3 مراكز
- **الأدوية**: 8 أدوية
- **وسائل منع الحمل**: 10 وسائل

## 🔧 تحديث ملفات التهيئة

### ملف `config/database.php`:
```php
private $host = '127.0.0.1';  // CloudPanel Host
private $db_name = 'csdb';
private $username = 'csdbuser';
private $password = 'j5aKN6lz5bsujTcWaYAd';
```

### ملف `config/database-live.php`:
```php
'host' => '127.0.0.1',  // CloudPanel Host
'dbname' => 'csdb',
'username' => 'csdbuser',
'password' => 'j5aKN6lz5bsujTcWaYAd',
```

## 🧪 اختبار سريع

### إنشاء ملف `test.php`:
```php
<?php
require_once 'config/database.php';
$db = new Database();
$pdo = $db->getConnection();
$stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
$result = $stmt->fetch();
echo "عدد المستخدمين: " . $result['count'];
?>
```

### تشغيل الاختبار:
- ارفع الملف عبر File Manager
- افتحه في المتصفح: `https://yourdomain.com/test.php`
- يجب أن يظهر: "عدد المستخدمين: 4"

## 🆘 في حالة المشاكل

### مشكلة: "Access denied"
**الحل**: تحقق من صلاحيات المستخدم في CloudPanel

### مشكلة: "Unknown database"
**الحل**: تأكد من إنشاء قاعدة البيانات `csdb`

### مشكلة: "File too large"
**الحل**: استخدم Terminal بدلاً من phpMyAdmin:
```bash
mysql -h 127.0.0.1 -u csdbuser -p csdb < database_complete_rebuild.sql
```

### مشكلة: "Character encoding"
**الحل**: تأكد من اختيار utf8mb4 في phpMyAdmin

## ✅ قائمة مراجعة سريعة

- [ ] تم استيراد الملف بنجاح
- [ ] يوجد 22 جدول في قاعدة البيانات
- [ ] جدول users يحتوي على 4 مستخدمين
- [ ] جدول vaccines يحتوي على 19 لقاح
- [ ] تم تحديث ملفات التهيئة
- [ ] تم اختبار الاتصال من التطبيق
- [ ] النصوص العربية تظهر بشكل صحيح

## 🎉 تم بنجاح!

الآن قاعدة البيانات جاهزة للاستخدام مع نظام إدارة المراكز الصحية!

---

## 📚 مراجع إضافية

- `CloudPanel_Restoration_Guide.md` - دليل مفصل
- `QUICK_START.md` - دليل البدء السريع العام
- `database_restoration_guide.md` - دليل شامل لجميع الطرق
