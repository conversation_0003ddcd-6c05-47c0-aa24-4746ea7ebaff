<?php
/**
 * فئة إدارة الأطفال
 * Children Management Class
 */

class ChildrenManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على قائمة الأطفال مع التصفح
     * Get children list with pagination
     */
    public function getChildren($page = 1, $limit = 20, $search = '', $nurse_id = null) {
        try {
            $offset = ($page - 1) * $limit;
            
            // بناء الاستعلام الأساسي
            $sql = "SELECT c.*, u.name as nurse_name, ct.name as center_name
                    FROM children c 
                    LEFT JOIN users u ON c.nurse_id = u.id 
                    LEFT JOIN centers ct ON c.center_id = ct.id";
            
            $count_sql = "SELECT COUNT(*) FROM children c";
            
            $where_conditions = [];
            $params = [];
            
            // إضافة شرط البحث
            if (!empty($search)) {
                $where_conditions[] = "(c.name LIKE ? OR c.parent_name LIKE ? OR c.parent_phone LIKE ?)";
                $search_param = "%{$search}%";
                $params[] = $search_param;
                $params[] = $search_param;
                $params[] = $search_param;
            }
            
            // إضافة شرط الممرض
            if ($nurse_id) {
                $where_conditions[] = "c.nurse_id = ?";
                $params[] = $nurse_id;
            }
            
            // إضافة شروط WHERE إذا وجدت
            if (!empty($where_conditions)) {
                $where_clause = " WHERE " . implode(" AND ", $where_conditions);
                $sql .= $where_clause;
                $count_sql .= $where_clause;
            }
            
            // إضافة الترتيب والحد
            $sql .= " ORDER BY c.created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            // تنفيذ استعلام البيانات
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنفيذ استعلام العد
            $count_params = array_slice($params, 0, -2); // إزالة limit و offset
            $count_stmt = $this->db->prepare($count_sql);
            $count_stmt->execute($count_params);
            $total = $count_stmt->fetchColumn();
            
            // إضافة معلومات التلقيحات لكل طفل
            foreach ($children as &$child) {
                $child['vaccinations'] = $this->getChildVaccinations($child['id']);
                $child['age_months'] = $this->calculateAgeInMonths($child['birth_date']);
            }
            
            return [
                'children' => $children,
                'total' => (int)$total,
                'page' => (int)$page,
                'limit' => (int)$limit,
                'total_pages' => ceil($total / $limit)
            ];
            
        } catch (Exception $e) {
            error_log('Get children error: ' . $e->getMessage());
            return [
                'children' => [],
                'total' => 0,
                'page' => 1,
                'limit' => $limit,
                'total_pages' => 0
            ];
        }
    }
    
    /**
     * الحصول على طفل بالمعرف
     * Get child by ID
     */
    public function getChild($id) {
        try {
            $sql = "SELECT c.*, u.name as nurse_name, ct.name as center_name
                    FROM children c 
                    LEFT JOIN users u ON c.nurse_id = u.id 
                    LEFT JOIN centers ct ON c.center_id = ct.id 
                    WHERE c.id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            $child = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($child) {
                $child['vaccinations'] = $this->getChildVaccinations($id);
                $child['age_months'] = $this->calculateAgeInMonths($child['birth_date']);
                $child['vaccination_schedule'] = $this->getVaccinationSchedule($id);
            }
            
            return $child;
            
        } catch (Exception $e) {
            error_log('Get child error: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * إضافة طفل جديد
     * Create new child
     */
    public function createChild($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['name', 'birth_date', 'nurse_id', 'center_id'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("الحقل {$field} مطلوب");
                }
            }
            
            // إنشاء معرف فريد
            $child_id = $this->generateChildId();
            
            $sql = "INSERT INTO children (id, name, birth_date, gender, parent_name, parent_phone, address, nurse_id, center_id) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $child_id,
                $data['name'],
                $data['birth_date'],
                $data['gender'] ?? null,
                $data['parent_name'] ?? null,
                $data['parent_phone'] ?? null,
                $data['address'] ?? null,
                $data['nurse_id'],
                $data['center_id']
            ]);
            
            if ($result) {
                // إنشاء جدول التلقيحات للطفل
                $this->createVaccinationSchedule($child_id, $data['birth_date']);
                
                return $this->getChild($child_id);
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Create child error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث بيانات طفل
     * Update child
     */
    public function updateChild($id, $data) {
        try {
            // التحقق من وجود الطفل
            $existing_child = $this->getChild($id);
            if (!$existing_child) {
                throw new Exception('الطفل غير موجود');
            }
            
            // بناء استعلام التحديث
            $update_fields = [];
            $params = [];
            
            $allowed_fields = ['name', 'birth_date', 'gender', 'parent_name', 'parent_phone', 'address', 'nurse_id'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                throw new Exception('لا توجد بيانات للتحديث');
            }
            
            $update_fields[] = "updated_at = CURRENT_TIMESTAMP";
            $params[] = $id;
            
            $sql = "UPDATE children SET " . implode(', ', $update_fields) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($params);
            
            if ($result) {
                // إذا تم تغيير تاريخ الميلاد، أعد إنشاء جدول التلقيحات
                if (isset($data['birth_date']) && $data['birth_date'] !== $existing_child['birth_date']) {
                    $this->updateVaccinationSchedule($id, $data['birth_date']);
                }
                
                return $this->getChild($id);
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Update child error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف طفل
     * Delete child
     */
    public function deleteChild($id) {
        try {
            // التحقق من وجود الطفل
            $child = $this->getChild($id);
            if (!$child) {
                throw new Exception('الطفل غير موجود');
            }
            
            // بدء معاملة
            $this->db->beginTransaction();
            
            // حذف التلقيحات أولاً
            $sql = "DELETE FROM child_vaccinations WHERE child_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            // حذف سجل استخدام اللقاحات
            $sql = "DELETE FROM vaccine_usage_log WHERE child_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            // حذف الطفل
            $sql = "DELETE FROM children WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->db->commit();
                return true;
            } else {
                $this->db->rollback();
                return false;
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log('Delete child error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على تلقيحات الطفل
     * Get child vaccinations
     */
    public function getChildVaccinations($child_id) {
        try {
            $sql = "SELECT cv.*, v.name_ar, v.name_fr, v.age_months
                    FROM child_vaccinations cv
                    JOIN vaccines v ON cv.vaccine_id = v.id
                    WHERE cv.child_id = ?
                    ORDER BY v.age_months";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$child_id]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get child vaccinations error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تسجيل تلقيح
     * Record vaccination
     */
    public function recordVaccination($child_id, $vaccine_id, $vaccination_date, $administered_by, $notes = '') {
        try {
            // التحقق من وجود الطفل واللقاح
            $child = $this->getChild($child_id);
            if (!$child) {
                throw new Exception('الطفل غير موجود');
            }
            
            // تحديث أو إدراج التلقيح
            $sql = "INSERT INTO child_vaccinations (child_id, vaccine_id, vaccination_date, is_completed, notes, administered_by)
                    VALUES (?, ?, ?, 1, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    vaccination_date = VALUES(vaccination_date),
                    is_completed = 1,
                    notes = VALUES(notes),
                    administered_by = VALUES(administered_by)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$child_id, $vaccine_id, $vaccination_date, $notes, $administered_by]);
            
            if ($result) {
                // تسجيل استخدام اللقاح
                $this->logVaccineUsage($vaccine_id, $administered_by, $child_id);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log('Record vaccination error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * إنشاء جدول التلقيحات للطفل
     * Create vaccination schedule for child
     */
    private function createVaccinationSchedule($child_id, $birth_date) {
        try {
            // الحصول على جميع اللقاحات النشطة
            $sql = "SELECT * FROM vaccines WHERE is_active = 1 ORDER BY age_months";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $vaccines = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($vaccines as $vaccine) {
                $due_date = date('Y-m-d', strtotime($birth_date . " + {$vaccine['age_months']} months"));
                
                $sql = "INSERT INTO child_vaccinations (child_id, vaccine_id, due_date, is_completed)
                        VALUES (?, ?, ?, 0)";
                
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$child_id, $vaccine['id'], $due_date]);
            }
            
        } catch (Exception $e) {
            error_log('Create vaccination schedule error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث جدول التلقيحات عند تغيير تاريخ الميلاد
     * Update vaccination schedule when birth date changes
     */
    private function updateVaccinationSchedule($child_id, $new_birth_date) {
        try {
            // الحصول على التلقيحات غير المكتملة
            $sql = "SELECT cv.*, v.age_months 
                    FROM child_vaccinations cv
                    JOIN vaccines v ON cv.vaccine_id = v.id
                    WHERE cv.child_id = ? AND cv.is_completed = 0";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$child_id]);
            $vaccinations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($vaccinations as $vaccination) {
                $new_due_date = date('Y-m-d', strtotime($new_birth_date . " + {$vaccination['age_months']} months"));
                
                $sql = "UPDATE child_vaccinations SET due_date = ? WHERE id = ?";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$new_due_date, $vaccination['id']]);
            }
            
        } catch (Exception $e) {
            error_log('Update vaccination schedule error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حساب العمر بالأشهر
     * Calculate age in months
     */
    private function calculateAgeInMonths($birth_date) {
        $birth = new DateTime($birth_date);
        $now = new DateTime();
        $interval = $birth->diff($now);
        
        return ($interval->y * 12) + $interval->m;
    }
    
    /**
     * إنشاء معرف طفل فريد
     * Generate unique child ID
     */
    private function generateChildId() {
        do {
            $id = 'child_' . uniqid();
            $sql = "SELECT COUNT(*) FROM children WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
        } while ($stmt->fetchColumn() > 0);
        
        return $id;
    }
    
    /**
     * تسجيل استخدام اللقاح
     * Log vaccine usage
     */
    private function logVaccineUsage($vaccine_id, $user_id, $child_id, $quantity = 1) {
        try {
            $sql = "INSERT INTO vaccine_usage_log (vaccine_id, user_id, child_id, quantity_used)
                    VALUES (?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$vaccine_id, $user_id, $child_id, $quantity]);
            
        } catch (Exception $e) {
            error_log('Log vaccine usage error: ' . $e->getMessage());
        }
    }
    
    /**
     * الحصول على جدول التلقيحات
     * Get vaccination schedule
     */
    public function getVaccinationSchedule($child_id) {
        try {
            $sql = "SELECT cv.*, v.name_ar, v.name_fr, v.age_months,
                           CASE 
                               WHEN cv.is_completed = 1 THEN 'completed'
                               WHEN cv.due_date < CURDATE() THEN 'overdue'
                               WHEN cv.due_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'due_soon'
                               ELSE 'scheduled'
                           END as status
                    FROM child_vaccinations cv
                    JOIN vaccines v ON cv.vaccine_id = v.id
                    WHERE cv.child_id = ?
                    ORDER BY v.age_months";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$child_id]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get vaccination schedule error: ' . $e->getMessage());
            return [];
        }
    }
}
?>
