#!/bin/bash

# =============================================================================
# سكريبت استعادة قاعدة البيانات - نظام إدارة المراكز الصحية
# Healthcare Centers Management System Database Restoration Script
# =============================================================================

# الألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل الملونة
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_warning() {
    print_message $YELLOW "⚠️  $1"
}

print_info() {
    print_message $BLUE "ℹ️  $1"
}

# متغيرات قاعدة البيانات الافتراضية
DEFAULT_DB_HOST="127.0.0.1"
DEFAULT_DB_NAME="csdb"
DEFAULT_DB_USER="csdbuser"
DEFAULT_DB_PASS="j5aKN6lz5bsujTcWaYAd"
DEFAULT_SQL_FILE="database_complete_rebuild.sql"

# دالة عرض المساعدة
show_help() {
    echo "استخدام السكريبت:"
    echo "$0 [OPTIONS]"
    echo ""
    echo "الخيارات:"
    echo "  -h, --host HOST        عنوان خادم قاعدة البيانات (افتراضي: 127.0.0.1)"
    echo "  -d, --database NAME    اسم قاعدة البيانات (افتراضي: csdb)"
    echo "  -u, --user USER        اسم مستخدم قاعدة البيانات (افتراضي: csdbuser)"
    echo "  -p, --password PASS    كلمة مرور قاعدة البيانات"
    echo "  -f, --file FILE        ملف SQL للاستعادة (افتراضي: database_complete_rebuild.sql)"
    echo "  -c, --check-only       فحص الاتصال فقط بدون استعادة"
    echo "  -b, --backup           إنشاء نسخة احتياطية قبل الاستعادة"
    echo "  --help                 عرض هذه المساعدة"
    echo ""
    echo "أمثلة:"
    echo "  $0                                        # استعادة بالإعدادات الافتراضية"
    echo "  $0 -h 127.0.0.1 -d csdb -u csdbuser      # تحديد معاملات الاتصال"
    echo "  $0 -c                                     # فحص الاتصال فقط"
    echo "  $0 -b                                     # إنشاء نسخة احتياطية قبل الاستعادة"
}

# دالة فحص المتطلبات
check_requirements() {
    print_info "فحص المتطلبات..."
    
    # فحص وجود MySQL
    if ! command -v mysql &> /dev/null; then
        print_error "MySQL غير مثبت أو غير موجود في PATH"
        return 1
    fi
    
    # فحص وجود mysqldump
    if ! command -v mysqldump &> /dev/null; then
        print_error "mysqldump غير مثبت أو غير موجود في PATH"
        return 1
    fi
    
    print_success "جميع المتطلبات متوفرة"
    return 0
}

# دالة فحص الاتصال بقاعدة البيانات
test_connection() {
    local host=$1
    local user=$2
    local pass=$3
    
    print_info "فحص الاتصال بقاعدة البيانات..."
    
    if mysql -h "$host" -u "$user" -p"$pass" -e "SELECT 1;" &> /dev/null; then
        print_success "تم الاتصال بقاعدة البيانات بنجاح"
        return 0
    else
        print_error "فشل في الاتصال بقاعدة البيانات"
        print_error "تحقق من معاملات الاتصال (الخادم، اسم المستخدم، كلمة المرور)"
        return 1
    fi
}

# دالة فحص وجود قاعدة البيانات
check_database_exists() {
    local host=$1
    local user=$2
    local pass=$3
    local dbname=$4
    
    if mysql -h "$host" -u "$user" -p"$pass" -e "USE $dbname;" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# دالة إنشاء قاعدة البيانات
create_database() {
    local host=$1
    local user=$2
    local pass=$3
    local dbname=$4
    
    print_info "إنشاء قاعدة البيانات $dbname..."
    
    if mysql -h "$host" -u "$user" -p"$pass" -e "CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" &> /dev/null; then
        print_success "تم إنشاء قاعدة البيانات بنجاح"
        return 0
    else
        print_error "فشل في إنشاء قاعدة البيانات"
        return 1
    fi
}

# دالة إنشاء نسخة احتياطية
create_backup() {
    local host=$1
    local user=$2
    local pass=$3
    local dbname=$4
    
    local backup_dir="backups"
    local backup_file="$backup_dir/${dbname}_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    print_info "إنشاء نسخة احتياطية..."
    
    # إنشاء مجلد النسخ الاحتياطية
    mkdir -p "$backup_dir"
    
    if mysqldump -h "$host" -u "$user" -p"$pass" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --default-character-set=utf8mb4 \
        "$dbname" > "$backup_file" 2> /dev/null; then
        
        # ضغط النسخة الاحتياطية
        gzip "$backup_file"
        print_success "تم إنشاء النسخة الاحتياطية: $backup_file.gz"
        return 0
    else
        print_warning "فشل في إنشاء النسخة الاحتياطية (قد تكون قاعدة البيانات غير موجودة)"
        return 1
    fi
}

# دالة استعادة قاعدة البيانات
restore_database() {
    local host=$1
    local user=$2
    local pass=$3
    local dbname=$4
    local sql_file=$5
    
    print_info "بدء استعادة قاعدة البيانات من $sql_file..."
    
    # فحص وجود ملف SQL
    if [ ! -f "$sql_file" ]; then
        print_error "ملف SQL غير موجود: $sql_file"
        return 1
    fi
    
    # تعطيل فحص المفاتيح الخارجية مؤقتاً
    mysql -h "$host" -u "$user" -p"$pass" -e "SET FOREIGN_KEY_CHECKS = 0;" 2> /dev/null
    
    # استيراد البيانات
    if mysql -h "$host" -u "$user" -p"$pass" "$dbname" < "$sql_file" 2> import_errors.log; then
        # إعادة تفعيل فحص المفاتيح الخارجية
        mysql -h "$host" -u "$user" -p"$pass" -e "SET FOREIGN_KEY_CHECKS = 1;" 2> /dev/null
        
        print_success "تم استيراد قاعدة البيانات بنجاح"
        
        # حذف ملف الأخطاء إذا كان فارغاً
        if [ ! -s import_errors.log ]; then
            rm -f import_errors.log
        else
            print_warning "تم العثور على بعض التحذيرات في ملف import_errors.log"
        fi
        
        return 0
    else
        print_error "فشل في استيراد قاعدة البيانات"
        print_error "راجع ملف import_errors.log للتفاصيل"
        return 1
    fi
}

# دالة التحقق من نجاح الاستعادة
verify_restoration() {
    local host=$1
    local user=$2
    local pass=$3
    local dbname=$4
    
    print_info "التحقق من نجاح عملية الاستعادة..."
    
    # فحص عدد الجداول
    local table_count=$(mysql -h "$host" -u "$user" -p"$pass" "$dbname" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$dbname';" 2>/dev/null | tail -1)
    
    if [ "$table_count" -ge 20 ]; then
        print_success "تم العثور على $table_count جدول"
    else
        print_warning "تم العثور على $table_count جدول فقط (متوقع: 22 أو أكثر)"
    fi
    
    # فحص البيانات الأساسية
    local users_count=$(mysql -h "$host" -u "$user" -p"$pass" "$dbname" -e "SELECT COUNT(*) FROM users;" 2>/dev/null | tail -1)
    local vaccines_count=$(mysql -h "$host" -u "$user" -p"$pass" "$dbname" -e "SELECT COUNT(*) FROM vaccines;" 2>/dev/null | tail -1)
    local centers_count=$(mysql -h "$host" -u "$user" -p"$pass" "$dbname" -e "SELECT COUNT(*) FROM centers;" 2>/dev/null | tail -1)
    
    print_info "إحصائيات البيانات:"
    echo "  - المستخدمين: $users_count"
    echo "  - اللقاحات: $vaccines_count"
    echo "  - المراكز: $centers_count"
    
    if [ "$users_count" -gt 0 ] && [ "$vaccines_count" -gt 0 ] && [ "$centers_count" -gt 0 ]; then
        print_success "تم التحقق من البيانات الأساسية بنجاح"
        return 0
    else
        print_error "البيانات الأساسية غير مكتملة"
        return 1
    fi
}

# دالة عرض ملخص النتائج
show_summary() {
    local success=$1
    
    echo ""
    echo "=========================================="
    if [ $success -eq 0 ]; then
        print_success "تمت عملية استعادة قاعدة البيانات بنجاح!"
        echo ""
        print_info "الخطوات التالية:"
        echo "1. تحديث ملفات التهيئة في مجلد config/"
        echo "2. اختبار الاتصال من التطبيق"
        echo "3. إعداد النسخ الاحتياطي التلقائي"
        echo "4. مراجعة صلاحيات المستخدمين"
    else
        print_error "فشلت عملية استعادة قاعدة البيانات"
        echo ""
        print_info "للمساعدة في حل المشكلة:"
        echo "1. راجع ملف import_errors.log"
        echo "2. تحقق من صلاحيات المستخدم"
        echo "3. تأكد من صحة ملف SQL"
        echo "4. راجع دليل استكشاف الأخطاء"
    fi
    echo "=========================================="
}

# المتغيرات
DB_HOST="$DEFAULT_DB_HOST"
DB_NAME="$DEFAULT_DB_NAME"
DB_USER="$DEFAULT_DB_USER"
DB_PASS="$DEFAULT_DB_PASS"
SQL_FILE="$DEFAULT_SQL_FILE"
CHECK_ONLY=false
CREATE_BACKUP=false

# معالجة المعاملات
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -d|--database)
            DB_NAME="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -p|--password)
            DB_PASS="$2"
            shift 2
            ;;
        -f|--file)
            SQL_FILE="$2"
            shift 2
            ;;
        -c|--check-only)
            CHECK_ONLY=true
            shift
            ;;
        -b|--backup)
            CREATE_BACKUP=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "معامل غير معروف: $1"
            show_help
            exit 1
            ;;
    esac
done

# طلب كلمة المرور إذا لم يتم تمريرها
if [ -z "$DB_PASS" ]; then
    echo -n "أدخل كلمة مرور قاعدة البيانات: "
    read -s DB_PASS
    echo
fi

# عرض معلومات الاتصال
echo "=========================================="
print_info "معلومات الاتصال:"
echo "  الخادم: $DB_HOST"
echo "  قاعدة البيانات: $DB_NAME"
echo "  المستخدم: $DB_USER"
echo "  ملف SQL: $SQL_FILE"
echo "=========================================="

# بدء العملية
print_info "بدء عملية استعادة قاعدة البيانات..."

# فحص المتطلبات
if ! check_requirements; then
    exit 1
fi

# فحص الاتصال
if ! test_connection "$DB_HOST" "$DB_USER" "$DB_PASS"; then
    exit 1
fi

# إذا كان الهدف فحص الاتصال فقط
if [ "$CHECK_ONLY" = true ]; then
    print_success "فحص الاتصال تم بنجاح!"
    exit 0
fi

# إنشاء نسخة احتياطية إذا طُلب ذلك
if [ "$CREATE_BACKUP" = true ]; then
    if check_database_exists "$DB_HOST" "$DB_USER" "$DB_PASS" "$DB_NAME"; then
        create_backup "$DB_HOST" "$DB_USER" "$DB_PASS" "$DB_NAME"
    else
        print_warning "قاعدة البيانات غير موجودة، تخطي إنشاء النسخة الاحتياطية"
    fi
fi

# إنشاء قاعدة البيانات
if ! create_database "$DB_HOST" "$DB_USER" "$DB_PASS" "$DB_NAME"; then
    exit 1
fi

# استعادة قاعدة البيانات
if ! restore_database "$DB_HOST" "$DB_USER" "$DB_PASS" "$DB_NAME" "$SQL_FILE"; then
    show_summary 1
    exit 1
fi

# التحقق من نجاح الاستعادة
if ! verify_restoration "$DB_HOST" "$DB_USER" "$DB_PASS" "$DB_NAME"; then
    show_summary 1
    exit 1
fi

# عرض ملخص النجاح
show_summary 0

exit 0
