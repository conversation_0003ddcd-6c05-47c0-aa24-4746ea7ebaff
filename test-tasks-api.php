<?php
/**
 * اختبار API المهام
 */

require_once 'config/database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
    
    // فحص وجود جدول المهام
    $stmt = $conn->query('SHOW TABLES LIKE "tasks"');
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ جدول المهام موجود\n";
        
        // عرض بنية الجدول
        $stmt = $conn->query('DESCRIBE tasks');
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "\n📋 أعمدة جدول المهام:\n";
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']})\n";
        }
        
        // عد المهام الموجودة
        $stmt = $conn->query('SELECT COUNT(*) as count FROM tasks');
        $count = $stmt->fetch()['count'];
        echo "\n📊 عدد المهام الحالية: {$count}\n";
        
    } else {
        echo "❌ جدول المهام غير موجود\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
