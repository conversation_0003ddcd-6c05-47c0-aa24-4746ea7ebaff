<?php
/**
 * فئة إدارة المصادقة والأمان
 * Authentication and Security Manager Class
 */

class AuthManager {
    private $db;
    private $session_timeout = 3600; // ساعة واحدة
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * تسجيل الدخول
     * User login
     */
    public function login($username, $password) {
        try {
            // التحقق من البيانات المطلوبة
            if (empty($username) || empty($password)) {
                return false;
            }
            
            // البحث عن المستخدم
            $sql = "SELECT u.*, c.name as center_name 
                    FROM users u 
                    LEFT JOIN centers c ON u.center_id = c.id 
                    WHERE u.username = ? AND u.is_active = 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                $this->logLoginAttempt($username, false, 'المستخدم غير موجود');
                return false;
            }
            
            // التحقق من كلمة المرور
            if (!password_verify($password, $user['password'])) {
                $this->logLoginAttempt($username, false, 'كلمة مرور خاطئة');
                return false;
            }
            
            // إنشاء الجلسة
            $session_data = [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'name' => $user['name'],
                'role' => $user['role'],
                'center_id' => $user['center_id'],
                'center_name' => $user['center_name'],
                'login_time' => time(),
                'last_activity' => time()
            ];
            
            $_SESSION['user'] = $session_data;
            
            // تحديث آخر تسجيل دخول
            $this->updateLastLogin($user['id']);
            
            // تسجيل محاولة الدخول الناجحة
            $this->logLoginAttempt($username, true);
            
            // إرجاع بيانات المستخدم (بدون كلمة المرور)
            unset($user['password']);
            $user['center_name'] = $session_data['center_name'];
            
            return $user;
            
        } catch (Exception $e) {
            error_log('Login error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تسجيل الخروج
     * User logout
     */
    public function logout() {
        if (isset($_SESSION['user'])) {
            $this->logLogout($_SESSION['user']['user_id']);
            unset($_SESSION['user']);
        }
        
        session_destroy();
        return true;
    }
    
    /**
     * الحصول على المستخدم الحالي
     * Get current user
     */
    public function getCurrentUser() {
        if (!isset($_SESSION['user'])) {
            return null;
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        if (time() - $_SESSION['user']['last_activity'] > $this->session_timeout) {
            $this->logout();
            return null;
        }
        
        // تحديث آخر نشاط
        $_SESSION['user']['last_activity'] = time();
        
        return $_SESSION['user'];
    }
    
    /**
     * التحقق من الصلاحيات
     * Check permissions
     */
    public function hasPermission($required_role) {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }
        
        $role_hierarchy = [
            'nurse' => 1,
            'supervisor' => 2,
            'admin' => 3
        ];
        
        $user_level = $role_hierarchy[$user['role']] ?? 0;
        $required_level = $role_hierarchy[$required_role] ?? 0;
        
        return $user_level >= $required_level;
    }
    
    /**
     * التحقق من انتماء المستخدم لنفس المركز
     * Check if user belongs to same center
     */
    public function isSameCenter($center_id) {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }
        
        return $user['center_id'] == $center_id;
    }
    
    /**
     * تحديث آخر تسجيل دخول
     * Update last login
     */
    private function updateLastLogin($user_id) {
        try {
            $sql = "UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
        } catch (Exception $e) {
            error_log('Update last login error: ' . $e->getMessage());
        }
    }
    
    /**
     * تسجيل محاولات تسجيل الدخول
     * Log login attempts
     */
    private function logLoginAttempt($username, $success, $reason = '') {
        try {
            $sql = "INSERT INTO login_logs (username, success, ip_address, user_agent, reason, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())";
            
            // إنشاء الجدول إذا لم يكن موجوداً
            $this->createLoginLogsTable();
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $username,
                $success ? 1 : 0,
                $this->getRealIpAddr(),
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $reason
            ]);
        } catch (Exception $e) {
            error_log('Log login attempt error: ' . $e->getMessage());
        }
    }
    
    /**
     * تسجيل تسجيل الخروج
     * Log logout
     */
    private function logLogout($user_id) {
        try {
            $sql = "INSERT INTO logout_logs (user_id, ip_address, created_at) VALUES (?, ?, NOW())";
            
            // إنشاء الجدول إذا لم يكن موجوداً
            $this->createLogoutLogsTable();
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $this->getRealIpAddr()]);
        } catch (Exception $e) {
            error_log('Log logout error: ' . $e->getMessage());
        }
    }
    
    /**
     * إنشاء جدول سجلات تسجيل الدخول
     * Create login logs table
     */
    private function createLoginLogsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS login_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(100) NOT NULL,
                success BOOLEAN NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                reason VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->db->exec($sql);
        } catch (Exception $e) {
            error_log('Create login logs table error: ' . $e->getMessage());
        }
    }
    
    /**
     * إنشاء جدول سجلات تسجيل الخروج
     * Create logout logs table
     */
    private function createLogoutLogsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS logout_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id VARCHAR(50) NOT NULL,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->db->exec($sql);
        } catch (Exception $e) {
            error_log('Create logout logs table error: ' . $e->getMessage());
        }
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     * Get real IP address
     */
    private function getRealIpAddr() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '';
        }
    }
    
    /**
     * تغيير كلمة المرور
     * Change password
     */
    public function changePassword($user_id, $old_password, $new_password) {
        try {
            // التحقق من كلمة المرور القديمة
            $sql = "SELECT password FROM users WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user || !password_verify($old_password, $user['password'])) {
                return false;
            }
            
            // تحديث كلمة المرور
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            
            return $stmt->execute([$hashed_password, $user_id]);
            
        } catch (Exception $e) {
            error_log('Change password error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إعادة تعيين كلمة المرور
     * Reset password
     */
    public function resetPassword($username, $new_password) {
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE username = ?";
            $stmt = $this->db->prepare($sql);
            
            return $stmt->execute([$hashed_password, $username]);
            
        } catch (Exception $e) {
            error_log('Reset password error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من قوة كلمة المرور
     * Check password strength
     */
    public function checkPasswordStrength($password) {
        $score = 0;
        $feedback = [];
        
        // الطول
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
        }
        
        // أحرف كبيرة
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        // أحرف صغيرة
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        // أرقام
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        // رموز خاصة
        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        $strength_levels = [
            0 => 'ضعيف جداً',
            1 => 'ضعيف',
            2 => 'متوسط',
            3 => 'جيد',
            4 => 'قوي',
            5 => 'قوي جداً'
        ];
        
        return [
            'score' => $score,
            'strength' => $strength_levels[$score],
            'feedback' => $feedback,
            'is_strong' => $score >= 4
        ];
    }
}
?>
