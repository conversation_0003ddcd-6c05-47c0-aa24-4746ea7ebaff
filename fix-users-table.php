<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== إصلاح جدول المستخدمين ===\n\n";

// تضمين إعدادات قاعدة البيانات
require_once 'config/database-live.php';

try {
    $pdo = getDatabaseConnection();
    
    echo "1. فحص بنية الجدول الحالية:\n";
    
    // فحص الأعمدة الموجودة
    $stmt = $pdo->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "الأعمدة الموجودة:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    
    echo "\n2. إضافة الأعمدة المفقودة:\n";
    
    // قائمة الأعمدة المطلوبة
    $requiredColumns = [
        'center' => 'VARCHAR(200) DEFAULT NULL',
        'region' => 'VARCHAR(200) DEFAULT NULL', 
        'center_id' => 'VARCHAR(50) DEFAULT NULL',
        'role' => 'VARCHAR(50) DEFAULT \'nurse\'',
        'accountType' => 'VARCHAR(50) DEFAULT \'nurse\'',
        'last_login' => 'TIMESTAMP NULL',
        'is_active' => 'BOOLEAN DEFAULT TRUE'
    ];
    
    $existingColumns = array_column($columns, 'Field');
    
    foreach ($requiredColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $sql = "ALTER TABLE users ADD COLUMN $columnName $columnDefinition";
                $pdo->exec($sql);
                echo "✅ تم إضافة العمود: $columnName\n";
            } catch (PDOException $e) {
                echo "❌ خطأ في إضافة العمود $columnName: " . $e->getMessage() . "\n";
            }
        } else {
            echo "ℹ️ العمود $columnName موجود بالفعل\n";
        }
    }
    
    echo "\n3. تحديث البيانات الموجودة:\n";
    
    // تحديث المستخدمين الذين لا يملكون قيم للأعمدة الجديدة
    $updateQueries = [
        "UPDATE users SET center = 'المركز الصحي الرئيسي' WHERE center IS NULL OR center = ''",
        "UPDATE users SET region = 'المنطقة الأولى' WHERE region IS NULL OR region = ''",
        "UPDATE users SET center_id = '1' WHERE center_id IS NULL OR center_id = ''",
        "UPDATE users SET role = 'nurse' WHERE role IS NULL OR role = ''",
        "UPDATE users SET accountType = 'nurse' WHERE accountType IS NULL OR accountType = ''",
        "UPDATE users SET is_active = TRUE WHERE is_active IS NULL"
    ];
    
    foreach ($updateQueries as $query) {
        try {
            $result = $pdo->exec($query);
            echo "✅ تم تحديث $result صف\n";
        } catch (PDOException $e) {
            echo "❌ خطأ في التحديث: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n4. فحص البنية النهائية:\n";
    
    $stmt = $pdo->prepare("DESCRIBE users");
    $stmt->execute();
    $finalColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "الأعمدة النهائية:\n";
    foreach ($finalColumns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Default']}\n";
    }
    
    echo "\n5. عرض المستخدمين:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, username, name, center, region, role, accountType, is_active, created_at 
        FROM users 
        WHERE is_active = TRUE 
        ORDER BY name
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "عدد المستخدمين النشطين: " . count($users) . "\n\n";
    
    foreach ($users as $user) {
        echo "- {$user['name']} ({$user['username']})\n";
        echo "  المركز: {$user['center']}\n";
        echo "  المنطقة: {$user['region']}\n";
        echo "  النوع: {$user['role']} / {$user['accountType']}\n";
        echo "  تاريخ الإنشاء: {$user['created_at']}\n\n";
    }
    
    echo "6. اختبار user-management-api.php:\n";
    
    // اختبار API
    $testData = json_encode(['action' => 'load_all_users']);
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $testData,
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents("http://" . $_SERVER['HTTP_HOST'] . "/user-management-api.php", false, $context);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "✅ user-management-api.php يعمل بنجاح\n";
            echo "عدد المستخدمين من API: " . $data['count'] . "\n";
        } else {
            echo "❌ user-management-api.php فشل: " . ($data['message'] ?? 'خطأ غير معروف') . "\n";
        }
    } else {
        echo "❌ user-management-api.php لا يستجيب\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n=== انتهى الإصلاح ===\n";
?>
