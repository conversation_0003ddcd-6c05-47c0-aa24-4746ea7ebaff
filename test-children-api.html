<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الأطفال</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API الأطفال</h1>
        
        <div class="test-section">
            <h3>1. إضافة طفل جديد</h3>
            <div>
                <label>المستخدم:</label>
                <select id="userSelect">
                    <option value="admin_001">admin_001 - مدير النظام</option>
                    <option value="nurse_001">nurse_001 - الممرضة فاطمة أحمد</option>
                    <option value="nurse_002">nurse_002 - الممرضة عائشة محمد</option>
                </select>
            </div>
            <div>
                <label>اسم الطفل:</label>
                <input type="text" id="childName" value="طفل تجريبي" placeholder="اسم الطفل">
            </div>
            <div>
                <label>تاريخ الميلاد:</label>
                <input type="date" id="birthDate" value="2024-01-15">
            </div>
            <div>
                <label>المركز:</label>
                <input type="number" id="centerId" value="1" placeholder="معرف المركز">
            </div>
            <button onclick="testAddChild()">إضافة طفل تجريبي</button>
            <div id="addResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. تحميل الأطفال</h3>
            <button onclick="testLoadChildren()">تحميل أطفال المستخدم</button>
            <div id="loadResult" class="result"></div>
        </div>
    </div>

    <script>
        async function testAddChild() {
            const resultDiv = document.getElementById('addResult');
            const userId = document.getElementById('userSelect').value;
            const childName = document.getElementById('childName').value;
            const birthDate = document.getElementById('birthDate').value;
            const centerId = document.getElementById('centerId').value;
            
            resultDiv.textContent = 'جاري الإضافة...';
            
            try {
                const response = await fetch('children-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save',
                        user_id: userId,
                        center_id: centerId,
                        name: childName,
                        birth_date: birthDate,
                        vaccination_dates: [],
                        completed_vaccinations: []
                    })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testLoadChildren() {
            const resultDiv = document.getElementById('loadResult');
            const userId = document.getElementById('userSelect').value;
            
            resultDiv.textContent = 'جاري التحميل...';
            
            try {
                const response = await fetch('children-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load',
                        user_id: userId
                    })
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
