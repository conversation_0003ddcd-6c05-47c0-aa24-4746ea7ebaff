<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إنشاء الحساب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار إنشاء الحساب وحفظ الإقليم</h1>
        
        <div class="section">
            <h3>1. إنشاء حساب جديد</h3>
            
            <div class="form-group">
                <label>اسم الممرض/ة:</label>
                <input type="text" id="testName" value="ممرض تجريبي" placeholder="الاسم الكامل">
            </div>
            
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" id="testUsername" value="test_nurse_001" placeholder="اسم المستخدم">
            </div>
            
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="testPassword" value="123456" placeholder="كلمة المرور">
            </div>
            
            <div class="form-group">
                <label>اسم المركز الصحي:</label>
                <input type="text" id="testCenter" value="المركز الصحي التجريبي" placeholder="اسم المركز">
            </div>
            
            <div class="form-group">
                <label>الإقليم/المنطقة:</label>
                <input type="text" id="testRegion" value="إقليم الرباط سلا القنيطرة" placeholder="اسم الإقليم">
            </div>
            
            <div class="form-group">
                <label>نوع الحساب:</label>
                <select id="testAccountType">
                    <option value="nurse">ممرض/ة</option>
                    <option value="head_nurse">ممرض/ة رئيسي/ة</option>
                </select>
            </div>
            
            <button onclick="testCreateAccount()">إنشاء الحساب التجريبي</button>
            <div id="createResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>2. اختبار تسجيل الدخول</h3>
            <p>بعد إنشاء الحساب، اختبر تسجيل الدخول للتحقق من حفظ الإقليم:</p>
            
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" id="loginUsername" value="test_nurse_001">
            </div>
            
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="loginPassword" value="123456">
            </div>
            
            <button onclick="testLogin()">تسجيل الدخول</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>3. حذف الحساب التجريبي</h3>
            <p>بعد الانتهاء من الاختبار، احذف الحساب التجريبي:</p>
            <button onclick="deleteTestAccount()">حذف الحساب التجريبي</button>
            <div id="deleteResult" class="result"></div>
        </div>
    </div>

    <script>
        async function testCreateAccount() {
            const resultDiv = document.getElementById('createResult');
            resultDiv.textContent = 'جاري إنشاء الحساب...';
            
            const name = document.getElementById('testName').value;
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            const center = document.getElementById('testCenter').value;
            const region = document.getElementById('testRegion').value;
            const accountType = document.getElementById('testAccountType').value;
            
            try {
                const response = await fetch('create-account.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name,
                        username: username,
                        password: password,
                        center: center,
                        region: region,
                        role: accountType
                    })
                });
                
                const data = await response.json();
                
                let output = `📊 نتيجة إنشاء الحساب:\n`;
                output += `الحالة: ${data.success ? '✅ نجح' : '❌ فشل'}\n`;
                output += `الرسالة: ${data.message}\n\n`;
                
                if (data.success && data.user) {
                    output += `📋 بيانات المستخدم المُنشأ:\n`;
                    output += `المعرف: ${data.user.id}\n`;
                    output += `الاسم: ${data.user.name}\n`;
                    output += `اسم المستخدم: ${data.user.username}\n`;
                    output += `الدور: ${data.user.role}\n`;
                    output += `المركز: ${data.user.center}\n`;
                    output += `الإقليم: ${data.user.region} ${data.user.region === region ? '✅' : '❌'}\n`;
                }
                
                resultDiv.textContent = output;
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
            } catch (error) {
                resultDiv.textContent = '❌ خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = 'جاري تسجيل الدخول...';
            
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const response = await fetch('auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                let output = `📊 نتيجة تسجيل الدخول:\n`;
                output += `الحالة: ${data.success ? '✅ نجح' : '❌ فشل'}\n`;
                output += `الرسالة: ${data.message}\n\n`;
                
                if (data.success && data.user) {
                    output += `📋 بيانات المستخدم من قاعدة البيانات:\n`;
                    output += `المعرف: ${data.user.id}\n`;
                    output += `الاسم: ${data.user.name}\n`;
                    output += `اسم المستخدم: ${data.user.username}\n`;
                    output += `الدور: ${data.user.role}\n`;
                    output += `المركز: ${data.user.center}\n`;
                    output += `الإقليم: ${data.user.region}\n\n`;
                    
                    const expectedRegion = document.getElementById('testRegion').value;
                    if (data.user.region === expectedRegion) {
                        output += `✅ الإقليم محفوظ بشكل صحيح!`;
                    } else {
                        output += `❌ مشكلة في حفظ الإقليم!\n`;
                        output += `المتوقع: ${expectedRegion}\n`;
                        output += `الفعلي: ${data.user.region}`;
                    }
                }
                
                resultDiv.textContent = output;
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                
            } catch (error) {
                resultDiv.textContent = '❌ خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function deleteTestAccount() {
            const resultDiv = document.getElementById('deleteResult');
            resultDiv.textContent = 'جاري حذف الحساب...';
            
            try {
                // هذا يتطلب إنشاء API للحذف، لكن يمكن حذفه يدوياً من قاعدة البيانات
                resultDiv.textContent = '⚠️ يجب حذف الحساب يدوياً من قاعدة البيانات:\nDELETE FROM users WHERE username = "test_nurse_001";';
                resultDiv.className = 'result';
                
            } catch (error) {
                resultDiv.textContent = '❌ خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
