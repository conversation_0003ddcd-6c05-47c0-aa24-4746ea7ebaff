<?php
/**
 * فئة الاستجابة الموحدة للـ API
 * Unified API Response Class
 */

class ApiResponse {
    
    /**
     * إرسال استجابة نجاح
     * Send success response
     */
    public static function success($data = null, $message = 'تم بنجاح', $code = 200) {
        http_response_code($code);
        
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $data
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * إرسال استجابة خطأ
     * Send error response
     */
    public static function error($message = 'حدث خطأ', $code = 400, $details = null) {
        http_response_code($code);
        
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'error_code' => $code
        ];
        
        if ($details !== null) {
            $response['details'] = $details;
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * إرسال استجابة مع بيانات مقسمة على صفحات
     * Send paginated response
     */
    public static function paginated($data, $total, $page, $limit, $message = 'تم بنجاح') {
        $total_pages = ceil($total / $limit);
        
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $data,
            'pagination' => [
                'current_page' => (int)$page,
                'total_pages' => $total_pages,
                'total_items' => (int)$total,
                'items_per_page' => (int)$limit,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            ]
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * إرسال استجابة مع إحصائيات
     * Send response with statistics
     */
    public static function withStats($data, $stats, $message = 'تم بنجاح') {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $data,
            'stats' => $stats
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * التحقق من صحة البيانات المطلوبة
     * Validate required fields
     */
    public static function validateRequired($data, $required_fields) {
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (!empty($missing_fields)) {
            self::error(
                'حقول مطلوبة مفقودة: ' . implode(', ', $missing_fields),
                422,
                ['missing_fields' => $missing_fields]
            );
        }
        
        return true;
    }
    
    /**
     * تنظيف وتحضير البيانات للإرسال
     * Clean and prepare data for sending
     */
    public static function cleanData($data) {
        if (is_array($data)) {
            return array_map([self::class, 'cleanData'], $data);
        }
        
        if (is_object($data)) {
            return (array)$data;
        }
        
        if (is_string($data)) {
            return trim($data);
        }
        
        return $data;
    }
    
    /**
     * إرسال استجابة تحميل ملف
     * Send file download response
     */
    public static function downloadFile($file_path, $file_name = null, $content_type = 'application/octet-stream') {
        if (!file_exists($file_path)) {
            self::error('الملف غير موجود', 404);
        }
        
        if (!$file_name) {
            $file_name = basename($file_path);
        }
        
        header('Content-Type: ' . $content_type);
        header('Content-Disposition: attachment; filename="' . $file_name . '"');
        header('Content-Length: ' . filesize($file_path));
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');
        
        readfile($file_path);
        exit();
    }
    
    /**
     * إرسال استجابة JSON خام
     * Send raw JSON response
     */
    public static function json($data, $code = 200) {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * إرسال استجابة نص خام
     * Send raw text response
     */
    public static function text($text, $code = 200) {
        http_response_code($code);
        header('Content-Type: text/plain; charset=utf-8');
        echo $text;
        exit();
    }
    
    /**
     * إرسال استجابة HTML
     * Send HTML response
     */
    public static function html($html, $code = 200) {
        http_response_code($code);
        header('Content-Type: text/html; charset=utf-8');
        echo $html;
        exit();
    }
    
    /**
     * إرسال استجابة إعادة توجيه
     * Send redirect response
     */
    public static function redirect($url, $code = 302) {
        http_response_code($code);
        header('Location: ' . $url);
        exit();
    }
    
    /**
     * تسجيل الأخطاء
     * Log errors
     */
    public static function logError($message, $context = []) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'context' => $context,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? ''
        ];
        
        error_log(json_encode($log_entry, JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * التحقق من نوع المحتوى
     * Check content type
     */
    public static function isJsonRequest() {
        $content_type = $_SERVER['CONTENT_TYPE'] ?? '';
        return strpos($content_type, 'application/json') !== false;
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     * Get real IP address
     */
    public static function getRealIpAddr() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     * Validate email
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     * Validate phone number
     */
    public static function validatePhone($phone) {
        // تنسيق أرقام الهاتف المغربية
        $pattern = '/^(\+212|0)[5-7][0-9]{8}$/';
        return preg_match($pattern, $phone);
    }
    
    /**
     * تنظيف النص من HTML
     * Strip HTML tags
     */
    public static function stripHtml($text) {
        return strip_tags(trim($text));
    }
    
    /**
     * تشفير كلمة المرور
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * التحقق من كلمة المرور
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
}
?>
