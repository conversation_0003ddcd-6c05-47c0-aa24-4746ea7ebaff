# دليل استعادة قاعدة البيانات عبر CloudPanel

## 📋 نظرة عامة
هذا الدليل مخصص لاستعادة قاعدة البيانات باستخدام CloudPanel على خادم Hostinger.

## 🔧 معلومات الاتصال المحدثة

بناءً على لقطة الشاشة المرفقة، إعدادات قاعدة البيانات هي:
- **Host**: `127.0.0.1`
- **Port**: `3306`
- **Database**: `csdb`
- **Username**: `csdbuser`
- **Password**: `j5aKN6lz5bsujTcWaYAd`

## 🚀 الطريقة الأولى: استيراد مباشر عبر phpMyAdmin

### 1. الوصول إلى phpMyAdmin:
1. في CloudPanel، اذهب إلى قسم "Database Users"
2. ابحث عن المستخدم `csdbuser`
3. انقر على "Manage" بجانب المستخدم
4. سيتم فتح phpMyAdmin تلقائياً مع تسجيل الدخول

### 2. اختيار قاعدة البيانات:
1. في phpMyAdmin، انقر على `csdb` في القائمة اليسرى
2. تأكد من أن قاعدة البيانات فارغة أو احذف الجداول الموجودة

### 3. استيراد البيانات:
1. انقر على تبويب "Import" في الأعلى
2. انقر على "Choose File" واختر ملف `database_complete_rebuild.sql`
3. تأكد من الإعدادات التالية:
   - **Format**: SQL
   - **Character set of the file**: utf8mb4
   - **Partial import**: غير مفعل
   - **Allow the interruption**: مفعل (للملفات الكبيرة)
4. انقر على "Go" لبدء الاستيراد

### 4. مراقبة عملية الاستيراد:
- ستظهر رسائل التقدم أثناء الاستيراد
- انتظر حتى ظهور رسالة "Import has been successfully finished"

## 🖥️ الطريقة الثانية: عبر Terminal في CloudPanel

### 1. فتح Terminal:
1. في CloudPanel، اذهب إلى قسم "Files"
2. انقر على "Terminal" أو "File Manager"
3. افتح Terminal جديد

### 2. رفع ملف SQL:
```bash
# إذا لم يكن الملف موجود على الخادم، ارفعه عبر File Manager أولاً
# أو استخدم wget إذا كان متاحاً عبر رابط

# التأكد من وجود الملف
ls -la database_complete_rebuild.sql
```

### 3. تنفيذ الاستيراد:
```bash
# الاتصال واستيراد البيانات
mysql -h 127.0.0.1 -u csdbuser -p csdb < database_complete_rebuild.sql

# أدخل كلمة المرور عند الطلب: j5aKN6lz5bsujTcWaYAd
```

### 4. التحقق من النجاح:
```bash
# فحص الجداول
mysql -h 127.0.0.1 -u csdbuser -p csdb -e "SHOW TABLES;"

# فحص عدد الجداول (يجب أن يكون 22)
mysql -h 127.0.0.1 -u csdbuser -p csdb -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='csdb';"

# فحص البيانات الأساسية
mysql -h 127.0.0.1 -u csdbuser -p csdb -e "SELECT COUNT(*) as users FROM users; SELECT COUNT(*) as vaccines FROM vaccines; SELECT COUNT(*) as centers FROM centers;"
```

## 🔧 الطريقة الثالثة: استخدام السكريبت التلقائي

### 1. رفع السكريبت:
```bash
# رفع السكريبت عبر File Manager أو
# إنشاؤه مباشرة في Terminal

# منح صلاحية التنفيذ
chmod +x restore_database.sh
```

### 2. تشغيل السكريبت:
```bash
# تشغيل بالإعدادات الافتراضية (127.0.0.1)
./restore_database.sh

# أو تحديد المعاملات صراحة
./restore_database.sh -h 127.0.0.1 -d csdb -u csdbuser -p j5aKN6lz5bsujTcWaYAd
```

## ✅ التحقق من نجاح الاستعادة

### 1. فحص عبر phpMyAdmin:
1. اذهب إلى phpMyAdmin
2. اختر قاعدة البيانات `csdb`
3. تحقق من وجود 22 جدول
4. افتح بعض الجداول للتأكد من وجود البيانات

### 2. فحص عبر Terminal:
```bash
# عدد الجداول
mysql -h 127.0.0.1 -u csdbuser -p csdb -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='csdb';"

# البيانات الأساسية
mysql -h 127.0.0.1 -u csdbuser -p csdb -e "
SELECT 'Users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'Vaccines', COUNT(*) FROM vaccines  
UNION ALL
SELECT 'Centers', COUNT(*) FROM centers
UNION ALL
SELECT 'Medicines', COUNT(*) FROM medicines
UNION ALL
SELECT 'Contraceptives', COUNT(*) FROM contraceptives;
"
```

### 3. النتائج المتوقعة:
- **عدد الجداول**: 22
- **المستخدمين**: 4
- **اللقاحات**: 19
- **المراكز**: 3
- **الأدوية**: 8
- **وسائل منع الحمل**: 10

## 🔧 تحديث ملفات التهيئة

بعد نجاح الاستعادة، تأكد من تحديث ملفات التهيئة:

### 1. ملف config/database.php:
```php
<?php
class Database {
    private $host = '127.0.0.1';  // CloudPanel Host
    private $db_name = 'csdb';
    private $username = 'csdbuser';
    private $password = 'j5aKN6lz5bsujTcWaYAd';
    private $charset = 'utf8mb4';
    // باقي الكود...
}
?>
```

### 2. ملف config/database-live.php:
```php
<?php
$db_configs = [
    [
        'host' => '127.0.0.1',  // CloudPanel Host
        'dbname' => 'csdb',
        'username' => 'csdbuser',
        'password' => 'j5aKN6lz5bsujTcWaYAd',
        'charset' => 'utf8mb4'
    ]
];
?>
```

## 🧪 اختبار الاتصال من التطبيق

### 1. إنشاء ملف اختبار:
```php
<?php
// test_connection.php
require_once 'config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "✅ الاتصال بقاعدة البيانات نجح!\n";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✅ عدد المستخدمين: " . $result['count'] . "\n";
    
    // اختبار الترميز العربي
    $stmt = $pdo->query("SELECT name_ar FROM vaccines LIMIT 1");
    $result = $stmt->fetch();
    echo "✅ اختبار الترميز العربي: " . $result['name_ar'] . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage() . "\n";
}
?>
```

### 2. تشغيل الاختبار:
```bash
php test_connection.php
```

## 🆘 استكشاف الأخطاء الشائعة

### 1. خطأ "Access denied":
```bash
# التحقق من صلاحيات المستخدم في CloudPanel
# تأكد من أن المستخدم له صلاحيات "Read and Write" على قاعدة البيانات
```

### 2. خطأ "Unknown database":
```bash
# التأكد من وجود قاعدة البيانات
mysql -h 127.0.0.1 -u csdbuser -p -e "SHOW DATABASES;"
```

### 3. خطأ في الترميز:
```bash
# التحقق من ترميز قاعدة البيانات
mysql -h 127.0.0.1 -u csdbuser -p -e "SELECT SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME='csdb';"
```

### 4. ملف SQL كبير جداً:
```bash
# زيادة قيم المهلة الزمنية في phpMyAdmin
# أو تقسيم الملف إلى أجزاء أصغر
# أو استخدام Terminal بدلاً من phpMyAdmin
```

## 📋 قائمة مراجعة CloudPanel

- [ ] تم إنشاء قاعدة البيانات `csdb` بترميز utf8mb4_unicode_ci
- [ ] تم إنشاء المستخدم `csdbuser` مع صلاحيات كاملة
- [ ] تم استيراد ملف `database_complete_rebuild.sql` بنجاح
- [ ] تم التحقق من وجود 22 جدول
- [ ] تم التحقق من البيانات الافتراضية
- [ ] تم تحديث ملفات التهيئة
- [ ] تم اختبار الاتصال من التطبيق
- [ ] تعمل النصوص العربية بشكل صحيح

## 🎉 تم بنجاح!

الآن قاعدة البيانات جاهزة للاستخدام مع التطبيق على CloudPanel!
