<?php
/**
 * تحديث هيكل جدول الأطفال
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $updates = [];
    
    // فحص الأعمدة الموجودة
    $stmt = $pdo->query("DESCRIBE children");
    $columns = $stmt->fetchAll();
    $existingColumns = array_column($columns, 'Field');
    
    // إضافة الأعمدة المطلوبة إذا لم تكن موجودة
    if (!in_array('user_id', $existingColumns)) {
        try {
            $pdo->exec("ALTER TABLE children ADD COLUMN user_id VARCHAR(50) AFTER id");
            $updates[] = 'تم إضافة عمود user_id';
        } catch (Exception $e) {
            $updates[] = 'خطأ في إضافة user_id: ' . $e->getMessage();
        }
    }

    if (!in_array('vaccination_dates', $existingColumns)) {
        try {
            $pdo->exec("ALTER TABLE children ADD COLUMN vaccination_dates TEXT AFTER address");
            $updates[] = 'تم إضافة عمود vaccination_dates';
        } catch (Exception $e) {
            $updates[] = 'خطأ في إضافة vaccination_dates: ' . $e->getMessage();
        }
    }

    if (!in_array('completed_vaccinations', $existingColumns)) {
        try {
            $pdo->exec("ALTER TABLE children ADD COLUMN completed_vaccinations TEXT AFTER vaccination_dates");
            $updates[] = 'تم إضافة عمود completed_vaccinations';
        } catch (Exception $e) {
            $updates[] = 'خطأ في إضافة completed_vaccinations: ' . $e->getMessage();
        }
    }
    
    // تحديث نوع center_id إلى VARCHAR إذا كان INT
    $centerIdColumn = array_filter($columns, function($col) {
        return $col['Field'] === 'center_id';
    });

    if (!empty($centerIdColumn)) {
        $centerIdType = reset($centerIdColumn)['Type'];
        if (strpos($centerIdType, 'int') !== false) {
            try {
                // إزالة القيود الخارجية أولاً
                $stmt = $pdo->query("
                    SELECT CONSTRAINT_NAME
                    FROM information_schema.KEY_COLUMN_USAGE
                    WHERE TABLE_NAME = 'children'
                    AND COLUMN_NAME = 'center_id'
                    AND CONSTRAINT_NAME != 'PRIMARY'
                    AND TABLE_SCHEMA = DATABASE()
                ");
                $constraints = $stmt->fetchAll();

                foreach ($constraints as $constraint) {
                    try {
                        $pdo->exec("ALTER TABLE children DROP FOREIGN KEY " . $constraint['CONSTRAINT_NAME']);
                        $updates[] = 'تم إزالة القيد الخارجي: ' . $constraint['CONSTRAINT_NAME'];
                    } catch (Exception $e) {
                        // تجاهل الخطأ إذا كان القيد غير موجود
                    }
                }

                // الآن تحديث نوع العمود
                $pdo->exec("ALTER TABLE children MODIFY COLUMN center_id VARCHAR(50)");
                $updates[] = 'تم تحديث نوع عمود center_id إلى VARCHAR(50)';

            } catch (Exception $e) {
                $updates[] = 'خطأ في تحديث center_id: ' . $e->getMessage();
            }
        }
    }
    
    // إضافة فهارس إذا لم تكن موجودة
    try {
        $pdo->exec("CREATE INDEX idx_user_id ON children (user_id)");
        $updates[] = 'تم إضافة فهرس user_id';
    } catch (Exception $e) {
        // الفهرس موجود بالفعل
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_center_id_new ON children (center_id)");
        $updates[] = 'تم إضافة فهرس center_id';
    } catch (Exception $e) {
        // الفهرس موجود بالفعل
    }
    
    // فحص الهيكل النهائي
    $stmt = $pdo->query("DESCRIBE children");
    $finalColumns = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'updates' => $updates,
        'final_columns' => $finalColumns,
        'message' => count($updates) > 0 ? 'تم تحديث الجدول بنجاح' : 'الجدول محدث بالفعل'
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
