<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص بيانات الأطفال</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص بيانات الأطفال</h1>
        
        <div class="section">
            <h3>1. فحص جميع الأطفال في قاعدة البيانات</h3>
            <button onclick="checkAllChildren()">عرض جميع الأطفال</button>
            <div id="allChildrenResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>2. فحص أطفال مستخدم محدد</h3>
            <select id="userSelect">
                <option value="admin_001">admin_001 - مدير النظام</option>
                <option value="nurse_001">nurse_001 - الممرضة فاطمة أحمد</option>
                <option value="nurse_002">nurse_002 - الممرضة عائشة محمد</option>
                <option value="supervisor_001">supervisor_001 - المشرفة خديجة علي</option>
            </select>
            <button onclick="checkUserChildren()">فحص أطفال المستخدم</button>
            <div id="userChildrenResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>3. فحص أطفال مركز محدد</h3>
            <select id="centerSelect">
                <option value="1">المركز 1</option>
                <option value="2">المركز 2</option>
                <option value="3">المركز 3</option>
                <option value="4">المركز 4</option>
            </select>
            <button onclick="checkCenterChildren()">فحص أطفال المركز</button>
            <div id="centerChildrenResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>4. إحصائيات سريعة</h3>
            <button onclick="getQuickStats()">عرض الإحصائيات</button>
            <div id="statsResult" class="result"></div>
        </div>
    </div>

    <script>
        async function checkAllChildren() {
            const resultDiv = document.getElementById('allChildrenResult');
            resultDiv.textContent = 'جاري التحميل...';
            
            try {
                // استخدام SQL مباشر لعرض جميع الأطفال
                const response = await fetch('children-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'debug_all'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    let output = `إجمالي الأطفال: ${data.children.length}\n\n`;
                    
                    if (data.children.length > 0) {
                        output += "قائمة الأطفال:\n";
                        output += "=" .repeat(80) + "\n";
                        
                        data.children.forEach((child, index) => {
                            output += `${index + 1}. الاسم: ${child.name}\n`;
                            output += `   المعرف: ${child.id}\n`;
                            output += `   تاريخ الميلاد: ${child.birth_date || child.birthDate}\n`;
                            output += `   المستخدم: ${child.nurse_id || child.user_id || 'غير محدد'}\n`;
                            output += `   المركز: ${child.center_id}\n`;
                            output += `   تاريخ الإنشاء: ${child.created_at}\n`;
                            output += "-".repeat(40) + "\n";
                        });
                    } else {
                        output += "لا توجد أطفال في قاعدة البيانات";
                    }
                    
                    resultDiv.textContent = output;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = 'خطأ: ' + data.message;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function checkUserChildren() {
            const resultDiv = document.getElementById('userChildrenResult');
            const userId = document.getElementById('userSelect').value;
            
            resultDiv.textContent = 'جاري التحميل...';
            
            try {
                const response = await fetch('children-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load',
                        user_id: userId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    let output = `أطفال المستخدم ${userId}: ${data.children.length}\n\n`;
                    
                    if (data.children.length > 0) {
                        data.children.forEach((child, index) => {
                            output += `${index + 1}. ${child.name} - ${child.birthDate}\n`;
                            output += `   المعرف: ${child.id}\n`;
                            output += `   مواعيد التلقيح: ${child.vaccinationDates ? child.vaccinationDates.length : 0}\n`;
                            output += `   التلقيحات المكتملة: ${child.completedVaccinations ? Object.keys(child.completedVaccinations).length : 0}\n`;
                            output += "-".repeat(40) + "\n";
                        });
                    } else {
                        output += "لا توجد أطفال لهذا المستخدم";
                    }
                    
                    resultDiv.textContent = output;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = 'خطأ: ' + data.message;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function checkCenterChildren() {
            const resultDiv = document.getElementById('centerChildrenResult');
            const centerId = document.getElementById('centerSelect').value;
            
            resultDiv.textContent = 'جاري التحميل...';
            
            try {
                const response = await fetch('children-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load_center',
                        center_id: centerId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    let output = `أطفال المركز ${centerId}: ${data.children.length}\n\n`;
                    
                    if (data.children.length > 0) {
                        data.children.forEach((child, index) => {
                            output += `${index + 1}. ${child.name}\n`;
                            output += `   المعرف: ${child.id}\n`;
                            output += `   تاريخ الميلاد: ${child.birth_date || child.birthDate}\n`;
                            output += `   الممرضة: ${child.nurse_name || 'غير محدد'}\n`;
                            output += "-".repeat(40) + "\n";
                        });
                    } else {
                        output += "لا توجد أطفال في هذا المركز";
                    }
                    
                    resultDiv.textContent = output;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = 'خطأ: ' + data.message;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function getQuickStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.textContent = 'جاري الحساب...';
            
            try {
                // جمع إحصائيات من جميع المستخدمين
                const users = ['admin_001', 'nurse_001', 'nurse_002', 'supervisor_001'];
                let totalChildren = 0;
                let userStats = {};
                
                for (const userId of users) {
                    const response = await fetch('children-api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'load',
                            user_id: userId
                        })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        userStats[userId] = data.children.length;
                        totalChildren += data.children.length;
                    }
                }
                
                let output = `📊 إحصائيات سريعة\n`;
                output += "=".repeat(30) + "\n";
                output += `إجمالي الأطفال: ${totalChildren}\n\n`;
                output += "توزيع الأطفال حسب المستخدم:\n";
                
                for (const [userId, count] of Object.entries(userStats)) {
                    output += `${userId}: ${count} طفل\n`;
                }
                
                resultDiv.textContent = output;
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent = 'خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
