/**
 * عميل API لنظام إدارة المراكز الصحية
 * API Client for Healthcare Centers Management System
 */

class ApiClient {
    constructor() {
        this.baseUrl = '/api';
        this.currentUser = null;
        this.isLoading = false;
    }

    /**
     * إرسال طلب HTTP
     * Send HTTP request
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        };

        const config = { ...defaultOptions, ...options };

        if (config.body && typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        try {
            this.showLoader();
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request Error:', error);
            this.showError(error.message);
            throw error;
        } finally {
            this.hideLoader();
        }
    }

    /**
     * طلبات GET
     * GET requests
     */
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    /**
     * طلبات POST
     * POST requests
     */
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: data
        });
    }

    /**
     * طلبات PUT
     * PUT requests
     */
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: data
        });
    }

    /**
     * طلبات DELETE
     * DELETE requests
     */
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // ==================== المصادقة ====================

    /**
     * تسجيل الدخول
     * Login
     */
    async login(username, password) {
        try {
            const response = await this.post('/auth/login', {
                username,
                password
            });

            if (response.success) {
                this.currentUser = response.data;
                this.onLoginSuccess(response.data);
                return response.data;
            }

            throw new Error(response.message);
        } catch (error) {
            this.onLoginError(error.message);
            throw error;
        }
    }

    /**
     * تسجيل الخروج
     * Logout
     */
    async logout() {
        try {
            await this.post('/auth/logout');
            this.currentUser = null;
            this.onLogoutSuccess();
        } catch (error) {
            console.error('Logout error:', error);
            // حتى لو فشل الطلب، نقوم بتسجيل الخروج محلياً
            this.currentUser = null;
            this.onLogoutSuccess();
        }
    }

    /**
     * التحقق من حالة تسجيل الدخول
     * Check login status
     */
    async checkAuth() {
        try {
            const response = await this.get('/auth/check');
            if (response.success) {
                this.currentUser = response.data;
                return response.data;
            }
            return null;
        } catch (error) {
            this.currentUser = null;
            return null;
        }
    }

    // ==================== إدارة المستخدمين ====================

    /**
     * الحصول على جميع المستخدمين
     * Get all users
     */
    async getUsers() {
        const response = await this.get('/users/list');
        return response.data;
    }

    /**
     * إنشاء مستخدم جديد
     * Create new user
     */
    async createUser(userData) {
        const response = await this.post('/users/create', userData);
        return response.data;
    }

    /**
     * تحديث مستخدم
     * Update user
     */
    async updateUser(userId, userData) {
        const response = await this.put(`/users/update/${userId}`, userData);
        return response.data;
    }

    /**
     * حذف مستخدم
     * Delete user
     */
    async deleteUser(userId) {
        const response = await this.delete(`/users/delete/${userId}`);
        return response.success;
    }

    // ==================== إدارة الأطفال ====================

    /**
     * الحصول على قائمة الأطفال
     * Get children list
     */
    async getChildren(page = 1, limit = 20, search = '') {
        const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            search
        });
        
        const response = await this.get(`/children/list?${params}`);
        return response.data;
    }

    /**
     * الحصول على طفل بالمعرف
     * Get child by ID
     */
    async getChild(childId) {
        const response = await this.get(`/children/get/${childId}`);
        return response.data;
    }

    /**
     * إضافة طفل جديد
     * Add new child
     */
    async addChild(childData) {
        const response = await this.post('/children/create', childData);
        return response.data;
    }

    /**
     * تحديث بيانات طفل
     * Update child data
     */
    async updateChild(childId, childData) {
        const response = await this.put(`/children/update/${childId}`, childData);
        return response.data;
    }

    /**
     * حذف طفل
     * Delete child
     */
    async deleteChild(childId) {
        const response = await this.delete(`/children/delete/${childId}`);
        return response.success;
    }

    // ==================== إدارة اللقاحات ====================

    /**
     * الحصول على جميع اللقاحات
     * Get all vaccines
     */
    async getVaccines() {
        const response = await this.get('/vaccines/list');
        return response.data;
    }

    /**
     * الحصول على مخزون اللقاحات
     * Get vaccine stock
     */
    async getVaccineStock() {
        const response = await this.get('/vaccines/stock');
        return response.data;
    }

    /**
     * تحديث مخزون لقاح
     * Update vaccine stock
     */
    async updateVaccineStock(vaccineId, quantity, expiryDate = null, batchNumber = null) {
        const response = await this.post('/vaccines/stock/update', {
            vaccine_id: vaccineId,
            quantity,
            expiry_date: expiryDate,
            batch_number: batchNumber
        });
        return response.data;
    }

    // ==================== إدارة الأدوية ====================

    /**
     * الحصول على جميع الأدوية
     * Get all medicines
     */
    async getMedicines() {
        const response = await this.get('/medicines/list');
        return response.data;
    }

    /**
     * الحصول على مخزون الأدوية
     * Get medicine stock
     */
    async getMedicineStock() {
        const response = await this.get('/medicines/stock');
        return response.data;
    }

    // ==================== إدارة الرسائل ====================

    /**
     * الحصول على المحادثات
     * Get conversations
     */
    async getConversations() {
        const response = await this.get('/messages/conversations');
        return response.data;
    }

    /**
     * إرسال رسالة
     * Send message
     */
    async sendMessage(receiverId, message, attachments = null) {
        const response = await this.post('/messages/send', {
            receiver_id: receiverId,
            message,
            attachments
        });
        return response.data;
    }

    /**
     * الحصول على رسائل المحادثة
     * Get conversation messages
     */
    async getMessages(partnerId, page = 1) {
        const response = await this.get(`/messages/conversation/${partnerId}?page=${page}`);
        return response.data;
    }

    // ==================== إدارة المهام ====================

    /**
     * الحصول على مهام المستخدم
     * Get user tasks
     */
    async getTasks(status = null) {
        const params = status ? `?status=${status}` : '';
        const response = await this.get(`/tasks/list${params}`);
        return response.data;
    }

    /**
     * إضافة مهمة جديدة
     * Add new task
     */
    async addTask(title, description = null, priority = 'medium', dueDate = null) {
        const response = await this.post('/tasks/create', {
            title,
            description,
            priority,
            due_date: dueDate
        });
        return response.data;
    }

    /**
     * تحديث مهمة
     * Update task
     */
    async updateTask(taskId, taskData) {
        const response = await this.put(`/tasks/update/${taskId}`, taskData);
        return response.data;
    }

    // ==================== إدارة الإشعارات ====================

    /**
     * الحصول على الإشعارات
     * Get notifications
     */
    async getNotifications(unreadOnly = false) {
        const params = unreadOnly ? '?unread_only=true' : '';
        const response = await this.get(`/notifications/list${params}`);
        return response.data;
    }

    /**
     * تمييز الإشعارات كمقروءة
     * Mark notifications as read
     */
    async markNotificationsAsRead(notificationIds) {
        const response = await this.post('/notifications/mark-read', {
            notification_ids: notificationIds
        });
        return response.success;
    }

    // ==================== الإحصائيات ====================

    /**
     * الحصول على الإحصائيات العامة
     * Get general statistics
     */
    async getStats() {
        const response = await this.get('/stats/general');
        return response.data;
    }

    // ==================== وظائف مساعدة ====================

    /**
     * عرض مؤشر التحميل
     * Show loader
     */
    showLoader() {
        if (this.isLoading) return;
        this.isLoading = true;
        
        const loader = document.querySelector('.loader-overlay');
        if (loader) {
            loader.classList.add('show');
        }
    }

    /**
     * إخفاء مؤشر التحميل
     * Hide loader
     */
    hideLoader() {
        this.isLoading = false;
        
        const loader = document.querySelector('.loader-overlay');
        if (loader) {
            loader.classList.remove('show');
        }
    }

    /**
     * عرض رسالة خطأ
     * Show error message
     */
    showError(message) {
        if (typeof showToast === 'function') {
            showToast(message, 'error', 5000);
        } else {
            alert(message);
        }
    }

    /**
     * عرض رسالة نجاح
     * Show success message
     */
    showSuccess(message) {
        if (typeof showToast === 'function') {
            showToast(message, 'success', 3000);
        }
    }

    // ==================== أحداث المصادقة ====================

    /**
     * عند نجاح تسجيل الدخول
     * On login success
     */
    onLoginSuccess(user) {
        console.log('تم تسجيل الدخول بنجاح:', user);
        this.showSuccess('تم تسجيل الدخول بنجاح');
        
        // إخفاء نموذج تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.style.display = 'none';
        }

        // عرض المحتوى الرئيسي
        const mainContent = document.getElementById('mainPage');
        if (mainContent) {
            mainContent.style.display = 'block';
        }

        // تحديث واجهة المستخدم
        this.updateUserInterface(user);
    }

    /**
     * عند فشل تسجيل الدخول
     * On login error
     */
    onLoginError(message) {
        console.error('فشل تسجيل الدخول:', message);
        this.showError(message);
    }

    /**
     * عند نجاح تسجيل الخروج
     * On logout success
     */
    onLogoutSuccess() {
        console.log('تم تسجيل الخروج بنجاح');
        
        // إعادة تحميل الصفحة أو إعادة توجيه للصفحة الرئيسية
        window.location.reload();
    }

    /**
     * تحديث واجهة المستخدم
     * Update user interface
     */
    updateUserInterface(user) {
        // تحديث اسم المستخدم في الواجهة
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(element => {
            element.textContent = user.name;
        });

        // تحديث اسم المركز
        const centerNameElements = document.querySelectorAll('.center-name');
        centerNameElements.forEach(element => {
            element.textContent = user.center_name || '';
        });

        // إظهار/إخفاء عناصر حسب الصلاحيات
        this.updatePermissionBasedElements(user);
    }

    /**
     * تحديث العناصر حسب الصلاحيات
     * Update permission-based elements
     */
    updatePermissionBasedElements(user) {
        const adminElements = document.querySelectorAll('.admin-only');
        const supervisorElements = document.querySelectorAll('.supervisor-only');

        if (user.role === 'admin') {
            adminElements.forEach(el => el.style.display = 'block');
            supervisorElements.forEach(el => el.style.display = 'block');
        } else if (user.role === 'supervisor') {
            adminElements.forEach(el => el.style.display = 'none');
            supervisorElements.forEach(el => el.style.display = 'block');
        } else {
            adminElements.forEach(el => el.style.display = 'none');
            supervisorElements.forEach(el => el.style.display = 'none');
        }
    }
}

// إنشاء مثيل عام من عميل API (إذا لم يكن موجوداً)
if (typeof apiClient === 'undefined') {
    var apiClient = new ApiClient();
}

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiClient;
}
