<?php
/**
 * فئة إدارة الأدوية
 * Medicine Management Class
 */

class MedicineManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على جميع الأدوية
     * Get all medicines
     */
    public function getAllMedicines() {
        try {
            $sql = "SELECT * FROM medicines WHERE is_active = 1 ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get all medicines error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مخزون الأدوية للمستخدم
     * Get medicine stock for user
     */
    public function getMedicineStock($user_id) {
        try {
            $sql = "SELECT ms.*, m.name, m.unit 
                    FROM medicine_stock ms
                    JOIN medicines m ON ms.medicine_id = m.id
                    WHERE ms.user_id = ?
                    ORDER BY m.name";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get medicine stock error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث مخزون دواء
     * Update medicine stock
     */
    public function updateMedicineStock($user_id, $medicine_id, $quantity, $expiry_date = null, $batch_number = null) {
        try {
            $sql = "INSERT INTO medicine_stock (medicine_id, user_id, quantity, expiry_date, batch_number)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    quantity = VALUES(quantity),
                    expiry_date = VALUES(expiry_date),
                    batch_number = VALUES(batch_number),
                    updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$medicine_id, $user_id, $quantity, $expiry_date, $batch_number]);
            
        } catch (Exception $e) {
            error_log('Update medicine stock error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إضافة دواء جديد
     * Add new medicine
     */
    public function addMedicine($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['id', 'name'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("الحقل {$field} مطلوب");
                }
            }
            
            // التحقق من عدم تكرار المعرف
            if ($this->medicineExists($data['id'])) {
                throw new Exception('معرف الدواء موجود مسبقاً');
            }
            
            $sql = "INSERT INTO medicines (id, name, unit, description, is_active)
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $data['id'],
                $data['name'],
                $data['unit'] ?? 'وحدة',
                $data['description'] ?? null,
                $data['is_active'] ?? 1
            ]);
            
        } catch (Exception $e) {
            error_log('Add medicine error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث دواء
     * Update medicine
     */
    public function updateMedicine($id, $data) {
        try {
            // التحقق من وجود الدواء
            if (!$this->medicineExists($id)) {
                throw new Exception('الدواء غير موجود');
            }
            
            // بناء استعلام التحديث
            $update_fields = [];
            $params = [];
            
            $allowed_fields = ['name', 'unit', 'description', 'is_active'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                throw new Exception('لا توجد بيانات للتحديث');
            }
            
            $params[] = $id;
            
            $sql = "UPDATE medicines SET " . implode(', ', $update_fields) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
            
        } catch (Exception $e) {
            error_log('Update medicine error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف دواء
     * Delete medicine
     */
    public function deleteMedicine($id) {
        try {
            // التحقق من وجود الدواء
            if (!$this->medicineExists($id)) {
                throw new Exception('الدواء غير موجود');
            }
            
            // حذف الدواء (سيتم حذف المخزون تلقائياً بسبب CASCADE)
            $sql = "DELETE FROM medicines WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$id]);
            
        } catch (Exception $e) {
            error_log('Delete medicine error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * التحقق من وجود الدواء
     * Check if medicine exists
     */
    private function medicineExists($id) {
        try {
            $sql = "SELECT COUNT(*) FROM medicines WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log('Medicine exists check error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات الأدوية
     * Get medicine statistics
     */
    public function getMedicineStats($user_id = null, $center_id = null) {
        try {
            $stats = [];
            
            // إجمالي الأدوية المتاحة
            $sql = "SELECT COUNT(*) FROM medicines WHERE is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['total_medicines'] = $stmt->fetchColumn();
            
            // إجمالي المخزون
            $sql = "SELECT SUM(quantity) FROM medicine_stock";
            $params = [];
            
            if ($user_id) {
                $sql .= " WHERE user_id = ?";
                $params[] = $user_id;
            } elseif ($center_id) {
                $sql .= " WHERE user_id IN (SELECT id FROM users WHERE center_id = ?)";
                $params[] = $center_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['total_stock'] = $stmt->fetchColumn() ?: 0;
            
            // الأدوية منتهية الصلاحية
            $sql = "SELECT COUNT(*) FROM medicine_stock 
                    WHERE expiry_date IS NOT NULL AND expiry_date < CURRENT_DATE()";
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " AND user_id IN (SELECT id FROM users WHERE center_id = ?)";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['expired_medicines'] = $stmt->fetchColumn() ?: 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Get medicine stats error: ' . $e->getMessage());
            return [];
        }
    }
}

/**
 * فئة إدارة وسائل منع الحمل
 * Contraceptive Management Class
 */

class ContraceptiveManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على جميع وسائل منع الحمل
     * Get all contraceptives
     */
    public function getAllContraceptives() {
        try {
            $sql = "SELECT * FROM contraceptives WHERE is_active = 1 ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get all contraceptives error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مخزون وسائل منع الحمل للمستخدم
     * Get contraceptive stock for user
     */
    public function getContraceptiveStock($user_id) {
        try {
            $sql = "SELECT cs.*, c.name, c.type 
                    FROM contraceptive_stock cs
                    JOIN contraceptives c ON cs.contraceptive_id = c.id
                    WHERE cs.user_id = ?
                    ORDER BY c.name";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get contraceptive stock error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث مخزون وسيلة منع الحمل
     * Update contraceptive stock
     */
    public function updateContraceptiveStock($user_id, $contraceptive_id, $quantity, $expiry_date = null, $batch_number = null) {
        try {
            $sql = "INSERT INTO contraceptive_stock (contraceptive_id, user_id, quantity, expiry_date, batch_number)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    quantity = VALUES(quantity),
                    expiry_date = VALUES(expiry_date),
                    batch_number = VALUES(batch_number),
                    updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$contraceptive_id, $user_id, $quantity, $expiry_date, $batch_number]);
            
        } catch (Exception $e) {
            error_log('Update contraceptive stock error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إضافة وسيلة منع حمل جديدة
     * Add new contraceptive
     */
    public function addContraceptive($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['id', 'name'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("الحقل {$field} مطلوب");
                }
            }
            
            // التحقق من عدم تكرار المعرف
            if ($this->contraceptiveExists($data['id'])) {
                throw new Exception('معرف وسيلة منع الحمل موجود مسبقاً');
            }
            
            $sql = "INSERT INTO contraceptives (id, name, type, description, is_active)
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $data['id'],
                $data['name'],
                $data['type'] ?? null,
                $data['description'] ?? null,
                $data['is_active'] ?? 1
            ]);
            
        } catch (Exception $e) {
            error_log('Add contraceptive error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * التحقق من وجود وسيلة منع الحمل
     * Check if contraceptive exists
     */
    private function contraceptiveExists($id) {
        try {
            $sql = "SELECT COUNT(*) FROM contraceptives WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log('Contraceptive exists check error: ' . $e->getMessage());
            return false;
        }
    }
}
?>
