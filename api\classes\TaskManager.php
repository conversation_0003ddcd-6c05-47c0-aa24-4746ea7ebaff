<?php
/**
 * فئة إدارة المهام
 * Task Management Class
 */

class TaskManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على مهام المستخدم
     * Get user tasks
     */
    public function getUserTasks($user_id, $status = null) {
        try {
            $sql = "SELECT * FROM tasks WHERE user_id = ?";
            $params = [$user_id];
            
            if ($status) {
                $sql .= " AND status = ?";
                $params[] = $status;
            }
            
            $sql .= " ORDER BY 
                        CASE 
                            WHEN priority = 'high' THEN 1
                            WHEN priority = 'medium' THEN 2
                            WHEN priority = 'low' THEN 3
                        END,
                        due_date ASC,
                        created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get user tasks error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إضافة مهمة جديدة
     * Add new task
     */
    public function addTask($user_id, $title, $description = null, $priority = 'medium', $due_date = null) {
        try {
            $sql = "INSERT INTO tasks (user_id, title, description, priority, due_date) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$user_id, $title, $description, $priority, $due_date]);
            
            if ($result) {
                return $this->db->lastInsertId();
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Add task error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث مهمة
     * Update task
     */
    public function updateTask($task_id, $user_id, $data) {
        try {
            // التحقق من ملكية المهمة
            $sql = "SELECT user_id FROM tasks WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$task_id]);
            $task = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$task || $task['user_id'] !== $user_id) {
                throw new Exception('غير مصرح بتعديل هذه المهمة');
            }
            
            // بناء استعلام التحديث
            $update_fields = [];
            $params = [];
            
            $allowed_fields = ['title', 'description', 'status', 'priority', 'due_date'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                throw new Exception('لا توجد بيانات للتحديث');
            }
            
            $update_fields[] = "updated_at = CURRENT_TIMESTAMP";
            $params[] = $task_id;
            
            $sql = "UPDATE tasks SET " . implode(', ', $update_fields) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
            
        } catch (Exception $e) {
            error_log('Update task error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف مهمة
     * Delete task
     */
    public function deleteTask($task_id, $user_id) {
        try {
            // التحقق من ملكية المهمة
            $sql = "SELECT user_id FROM tasks WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$task_id]);
            $task = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$task || $task['user_id'] !== $user_id) {
                throw new Exception('غير مصرح بحذف هذه المهمة');
            }
            
            $sql = "DELETE FROM tasks WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$task_id]);
            
        } catch (Exception $e) {
            error_log('Delete task error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على إحصائيات المهام
     * Get task statistics
     */
    public function getTaskStats($user_id) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_tasks,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
                        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_tasks,
                        SUM(CASE WHEN due_date < CURRENT_DATE() AND status NOT IN ('completed', 'cancelled') THEN 1 ELSE 0 END) as overdue_tasks
                    FROM tasks 
                    WHERE user_id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get task stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على المهام المتأخرة
     * Get overdue tasks
     */
    public function getOverdueTasks($user_id = null) {
        try {
            $sql = "SELECT t.*, u.name as user_name 
                    FROM tasks t
                    JOIN users u ON t.user_id = u.id
                    WHERE t.due_date < CURRENT_DATE() 
                    AND t.status NOT IN ('completed', 'cancelled')";
            
            $params = [];
            
            if ($user_id) {
                $sql .= " AND t.user_id = ?";
                $params[] = $user_id;
            }
            
            $sql .= " ORDER BY t.due_date ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get overdue tasks error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على المهام المستحقة قريباً
     * Get tasks due soon
     */
    public function getTasksDueSoon($user_id = null, $days = 7) {
        try {
            $sql = "SELECT t.*, u.name as user_name 
                    FROM tasks t
                    JOIN users u ON t.user_id = u.id
                    WHERE t.due_date BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL ? DAY)
                    AND t.status NOT IN ('completed', 'cancelled')";
            
            $params = [$days];
            
            if ($user_id) {
                $sql .= " AND t.user_id = ?";
                $params[] = $user_id;
            }
            
            $sql .= " ORDER BY t.due_date ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get tasks due soon error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تمييز المهمة كمكتملة
     * Mark task as completed
     */
    public function markTaskCompleted($task_id, $user_id) {
        try {
            return $this->updateTask($task_id, $user_id, ['status' => 'completed']);
        } catch (Exception $e) {
            error_log('Mark task completed error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء مهام تلقائية للتلقيحات المستحقة
     * Create automatic tasks for due vaccinations
     */
    public function createVaccinationTasks() {
        try {
            // البحث عن التلقيحات المستحقة خلال الأسبوع القادم
            $sql = "SELECT cv.*, c.name as child_name, c.nurse_id, v.name_ar as vaccine_name
                    FROM child_vaccinations cv
                    JOIN children c ON cv.child_id = c.id
                    JOIN vaccines v ON cv.vaccine_id = v.id
                    WHERE cv.is_completed = 0 
                    AND cv.due_date BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY)
                    AND c.nurse_id IS NOT NULL";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $due_vaccinations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($due_vaccinations as $vaccination) {
                // التحقق من عدم وجود مهمة مماثلة
                $check_sql = "SELECT COUNT(*) FROM tasks 
                             WHERE user_id = ? 
                             AND title LIKE ? 
                             AND created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
                             AND status NOT IN ('completed', 'cancelled')";
                
                $check_stmt = $this->db->prepare($check_sql);
                $check_stmt->execute([
                    $vaccination['nurse_id'],
                    "%{$vaccination['child_name']}%{$vaccination['vaccine_name']}%"
                ]);
                
                if ($check_stmt->fetchColumn() == 0) {
                    $title = "تلقيح مستحق: {$vaccination['child_name']} - {$vaccination['vaccine_name']}";
                    $description = "الطفل {$vaccination['child_name']} يحتاج لقاح {$vaccination['vaccine_name']} في تاريخ {$vaccination['due_date']}";
                    
                    $this->addTask(
                        $vaccination['nurse_id'],
                        $title,
                        $description,
                        'high',
                        $vaccination['due_date']
                    );
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('Create vaccination tasks error: ' . $e->getMessage());
            return false;
        }
    }
}
?>
