<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مواعيد التلقيح للطفل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مواعيد التلقيح للطفل</h1>
        
        <div class="section">
            <h3>إصلاح الطفل التجريبي</h3>
            <p><strong>الطفل:</strong> طفل تجريبي</p>
            <p><strong>تاريخ الميلاد:</strong> 2024-01-15</p>
            <p><strong>المعرف:</strong> 1753315618_6881792294ef0</p>
            
            <button onclick="fixChildVaccination()">حساب وإضافة مواعيد التلقيح</button>
            <div id="fixResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>التحقق من النتيجة</h3>
            <button onclick="checkChildAfterFix()">فحص الطفل بعد الإصلاح</button>
            <div id="checkResult" class="result"></div>
        </div>
    </div>

    <script>
        const childId = '1753315618_6881792294ef0';
        const childName = 'طفل تجريبي';
        const birthDate = '2024-01-15';
        const userId = 'admin_001';
        const centerId = 1;

        // دالة حساب مواعيد التلقيح
        function calculateVaccinationDates(birthDate) {
            const birth = new Date(birthDate);
            const vaccinations = [
                { name: 'BCG', ageInDays: 0, description: 'لقاح السل' },
                { name: 'Polio 0', ageInDays: 0, description: 'شلل الأطفال - الجرعة الأولى' },
                { name: 'Hepatitis B1', ageInDays: 0, description: 'التهاب الكبد ب - الجرعة الأولى' },
                { name: 'Polio 1', ageInDays: 60, description: 'شلل الأطفال - الجرعة الثانية' },
                { name: 'DTC-Hib-HepB 1', ageInDays: 60, description: 'اللقاح الخماسي - الجرعة الأولى' },
                { name: 'Pneumo 1', ageInDays: 60, description: 'المكورات الرئوية - الجرعة الأولى' },
                { name: 'Polio 2', ageInDays: 120, description: 'شلل الأطفال - الجرعة الثالثة' },
                { name: 'DTC-Hib-HepB 2', ageInDays: 120, description: 'اللقاح الخماسي - الجرعة الثانية' },
                { name: 'Pneumo 2', ageInDays: 120, description: 'المكورات الرئوية - الجرعة الثانية' },
                { name: 'Polio 3', ageInDays: 180, description: 'شلل الأطفال - الجرعة الرابعة' },
                { name: 'DTC-Hib-HepB 3', ageInDays: 180, description: 'اللقاح الخماسي - الجرعة الثالثة' },
                { name: 'Pneumo 3', ageInDays: 180, description: 'المكورات الرئوية - الجرعة الثالثة' },
                { name: 'RR1', ageInDays: 270, description: 'الحصبة والحصبة الألمانية - الجرعة الأولى' },
                { name: 'Pneumo Booster', ageInDays: 450, description: 'المكورات الرئوية - جرعة منشطة' },
                { name: 'DTC Booster', ageInDays: 540, description: 'الدفتيريا والتيتانوس والسعال الديكي - جرعة منشطة' },
                { name: 'RR2', ageInDays: 1800, description: 'الحصبة والحصبة الألمانية - الجرعة الثانية' }
            ];

            return vaccinations.map(vaccine => {
                const vaccinationDate = new Date(birth);
                vaccinationDate.setDate(birth.getDate() + vaccine.ageInDays);
                
                return {
                    vaccine: vaccine.name,
                    date: vaccinationDate.toISOString().split('T')[0],
                    description: vaccine.description,
                    ageInDays: vaccine.ageInDays
                };
            });
        }

        async function fixChildVaccination() {
            const resultDiv = document.getElementById('fixResult');
            resultDiv.textContent = 'جاري حساب وإضافة مواعيد التلقيح...';
            
            try {
                // حساب مواعيد التلقيح
                const vaccinationDates = calculateVaccinationDates(birthDate);
                
                resultDiv.textContent += '\n✅ تم حساب مواعيد التلقيح: ' + vaccinationDates.length + ' موعد';
                resultDiv.textContent += '\n\n📋 المواعيد المحسوبة:\n';
                
                vaccinationDates.forEach((vaccination, index) => {
                    resultDiv.textContent += `${index + 1}. ${vaccination.vaccine} - ${vaccination.date}\n`;
                });
                
                resultDiv.textContent += '\n🔄 جاري تحديث قاعدة البيانات...';
                
                // تحديث قاعدة البيانات
                const response = await fetch('children-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'update_vaccination_dates',
                        child_id: childId,
                        vaccination_dates: vaccinationDates,
                        completed_vaccinations: {}
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.textContent += '\n\n✅ تم تحديث قاعدة البيانات بنجاح!';
                    resultDiv.textContent += '\n📊 معرف الطفل: ' + data.child_id;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent += '\n\n❌ خطأ في تحديث قاعدة البيانات: ' + data.message;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent += '\n\n❌ خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function checkChildAfterFix() {
            const resultDiv = document.getElementById('checkResult');
            resultDiv.textContent = 'جاري فحص الطفل...';
            
            try {
                const response = await fetch('children-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load',
                        user_id: userId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const child = data.children.find(c => c.id === childId);
                    
                    if (child) {
                        let output = `✅ تم العثور على الطفل: ${child.name}\n`;
                        output += `📅 تاريخ الميلاد: ${child.birthDate}\n`;
                        output += `💉 مواعيد التلقيح: ${child.vaccinationDates ? child.vaccinationDates.length : 0}\n`;
                        output += `✅ التلقيحات المكتملة: ${child.completedVaccinations ? Object.keys(child.completedVaccinations).length : 0}\n\n`;
                        
                        if (child.vaccinationDates && child.vaccinationDates.length > 0) {
                            output += '📋 مواعيد التلقيح:\n';
                            child.vaccinationDates.forEach((vaccination, index) => {
                                output += `${index + 1}. ${vaccination.vaccine} - ${vaccination.date}\n`;
                            });
                        }
                        
                        resultDiv.textContent = output;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '❌ لم يتم العثور على الطفل';
                        resultDiv.className = 'result error';
                    }
                } else {
                    resultDiv.textContent = '❌ خطأ: ' + data.message;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = '❌ خطأ: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
