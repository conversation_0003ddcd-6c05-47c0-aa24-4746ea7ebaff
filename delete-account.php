<?php
/**
 * حذف حساب مستخدم
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // قراءة البيانات
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $account_id = $input['account_id'] ?? null;
    
    if (!$account_id) {
        throw new Exception('معرف الحساب مطلوب');
    }
    
    // التحقق من وجود الحساب
    $stmt = $pdo->prepare("SELECT id, name FROM users WHERE id = ? AND is_active = 1");
    $stmt->execute([$account_id]);
    $account = $stmt->fetch();
    
    if (!$account) {
        throw new Exception('الحساب غير موجود');
    }
    
    // حذف الحساب (تعطيل بدلاً من الحذف الفعلي)
    $stmt = $pdo->prepare("UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
    $stmt->execute([$account_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف الحساب بنجاح',
        'account_name' => $account['name']
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
