<?php
/**
 * ملف التثبيت التلقائي لنظام إدارة المراكز الصحية
 * Automatic Installation Script for Healthcare Centers Management System
 */

// منع الوصول المباشر إذا كان النظام مثبت مسبقاً
if (file_exists('config/installed.lock')) {
    die('النظام مثبت مسبقاً. إذا كنت تريد إعادة التثبيت، احذف ملف config/installed.lock');
}

// بدء الجلسة
session_start();

// معالجة طلب التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    handleInstallation();
}

function handleInstallation() {
    try {
        // التحقق من البيانات المرسلة
        $dbHost = $_POST['db_host'] ?? 'localhost';
        $dbName = $_POST['db_name'] ?? 'healthcare_management';
        $dbUser = $_POST['db_user'] ?? '';
        $dbPass = $_POST['db_pass'] ?? '';
        $adminUser = $_POST['admin_user'] ?? 'admin';
        $adminPass = $_POST['admin_pass'] ?? '';
        $adminName = $_POST['admin_name'] ?? 'مدير النظام';
        $centerName = $_POST['center_name'] ?? 'المركز الصحي الرئيسي';

        // التحقق من البيانات المطلوبة
        if (empty($dbUser) || empty($adminPass)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }

        // اختبار الاتصال بقاعدة البيانات
        $dsn = "mysql:host=$dbHost;charset=utf8mb4";
        $pdo = new PDO($dsn, $dbUser, $dbPass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbName`");

        // قراءة وتنفيذ سكريبت قاعدة البيانات
        $sqlFile = 'database_design.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('ملف قاعدة البيانات غير موجود');
        }

        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }

        // إنشاء المركز الصحي الافتراضي
        $stmt = $pdo->prepare("INSERT INTO centers (name, location) VALUES (?, ?) ON DUPLICATE KEY UPDATE name = VALUES(name)");
        $stmt->execute([$centerName, 'الموقع الافتراضي']);
        $centerId = $pdo->lastInsertId() ?: 1;

        // إنشاء المستخدم الإداري
        $hashedPassword = password_hash($adminPass, PASSWORD_DEFAULT);
        $adminId = 'admin_' . uniqid();
        
        $stmt = $pdo->prepare("INSERT INTO users (id, username, password, name, center_id, role) VALUES (?, ?, ?, ?, ?, 'admin') ON DUPLICATE KEY UPDATE password = VALUES(password), name = VALUES(name)");
        $stmt->execute([$adminId, $adminUser, $hashedPassword, $adminName, $centerId]);

        // إنشاء ملف الإعداد
        createConfigFile($dbHost, $dbName, $dbUser, $dbPass);

        // إنشاء ملف القفل
        createLockFile();

        // إنشاء المجلدات المطلوبة
        createDirectories();

        // رسالة النجاح
        $success = true;
        $message = 'تم تثبيت النظام بنجاح!';

    } catch (Exception $e) {
        $success = false;
        $message = 'خطأ في التثبيت: ' . $e->getMessage();
    }
}

function createConfigFile($host, $dbname, $username, $password) {
    $configContent = "<?php
/**
 * ملف الإعداد المُنشأ تلقائياً
 * Auto-generated configuration file
 */

class Database {
    private \$host = '$host';
    private \$db_name = '$dbname';
    private \$username = '$username';
    private \$password = '$password';
    private \$charset = 'utf8mb4';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;

        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci\"
            ];

            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
            
        } catch(PDOException \$exception) {
            error_log(\"Connection error: \" . \$exception->getMessage());
            throw new Exception(\"فشل في الاتصال بقاعدة البيانات\");
        }

        return \$this->conn;
    }
}
?>";

    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }

    file_put_contents('config/database.php', $configContent);
}

function createLockFile() {
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    $lockContent = "تم تثبيت النظام في: " . date('Y-m-d H:i:s');
    file_put_contents('config/installed.lock', $lockContent);
}

function createDirectories() {
    $directories = [
        'uploads',
        'backups',
        'logs',
        'temp'
    ];

    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }

    // إنشاء ملف .htaccess لحماية المجلدات الحساسة
    $htaccessContent = "Order deny,allow\nDeny from all";
    
    file_put_contents('config/.htaccess', $htaccessContent);
    file_put_contents('logs/.htaccess', $htaccessContent);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المراكز الصحية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            direction: rtl;
        }

        .install-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
        }

        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .install-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .install-header p {
            color: #666;
            font-size: 16px;
        }

        .form-section {
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group small {
            color: #888;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .btn-install {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-install:hover {
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .success-actions {
            text-align: center;
            margin-top: 20px;
        }

        .btn-continue {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn-continue:hover {
            background: #218838;
        }

        .requirements {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .requirements h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .requirements ul {
            margin-right: 20px;
        }

        .requirements li {
            margin-bottom: 5px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🏥 تثبيت نظام إدارة المراكز الصحية</h1>
            <p>مرحباً بك في معالج التثبيت</p>
        </div>

        <?php if (isset($success)): ?>
            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo $message; ?>
                </div>
                <div class="success-actions">
                    <a href="cs-manager-api.html" class="btn-continue">🚀 بدء استخدام النظام</a>
                    <a href="README.md" class="btn-continue">📖 قراءة الدليل</a>
                </div>
            <?php else: ?>
                <div class="alert alert-error">
                    ❌ <?php echo $message; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <?php if (!isset($success) || !$success): ?>
            <div class="requirements">
                <h4>متطلبات النظام:</h4>
                <ul>
                    <li>PHP 7.4 أو أحدث</li>
                    <li>MySQL 5.7 أو أحدث</li>
                    <li>Apache مع mod_rewrite</li>
                    <li>صلاحيات كتابة في مجلد المشروع</li>
                </ul>
            </div>

            <form method="POST">
                <div class="form-section">
                    <h3>🗄️ إعدادات قاعدة البيانات</h3>
                    
                    <div class="form-group">
                        <label for="db_host">عنوان الخادم</label>
                        <input type="text" id="db_host" name="db_host" value="localhost" required>
                        <small>عادة ما يكون localhost</small>
                    </div>

                    <div class="form-group">
                        <label for="db_name">اسم قاعدة البيانات</label>
                        <input type="text" id="db_name" name="db_name" value="healthcare_management" required>
                        <small>سيتم إنشاؤها تلقائياً إذا لم تكن موجودة</small>
                    </div>

                    <div class="form-group">
                        <label for="db_user">اسم المستخدم</label>
                        <input type="text" id="db_user" name="db_user" required>
                        <small>مستخدم قاعدة البيانات</small>
                    </div>

                    <div class="form-group">
                        <label for="db_pass">كلمة المرور</label>
                        <input type="password" id="db_pass" name="db_pass">
                        <small>كلمة مرور قاعدة البيانات</small>
                    </div>
                </div>

                <div class="form-section">
                    <h3>👤 إعدادات المدير</h3>
                    
                    <div class="form-group">
                        <label for="admin_user">اسم المستخدم الإداري</label>
                        <input type="text" id="admin_user" name="admin_user" value="admin" required>
                        <small>سيتم استخدامه لتسجيل الدخول</small>
                    </div>

                    <div class="form-group">
                        <label for="admin_pass">كلمة مرور المدير</label>
                        <input type="password" id="admin_pass" name="admin_pass" required>
                        <small>يُنصح بكلمة مرور قوية</small>
                    </div>

                    <div class="form-group">
                        <label for="admin_name">الاسم الكامل للمدير</label>
                        <input type="text" id="admin_name" name="admin_name" value="مدير النظام" required>
                    </div>
                </div>

                <div class="form-section">
                    <h3>🏥 إعدادات المركز</h3>
                    
                    <div class="form-group">
                        <label for="center_name">اسم المركز الصحي</label>
                        <input type="text" id="center_name" name="center_name" value="المركز الصحي الرئيسي" required>
                        <small>يمكن تعديله لاحقاً</small>
                    </div>
                </div>

                <button type="submit" class="btn-install">
                    🚀 بدء التثبيت
                </button>
            </form>
        <?php endif; ?>
    </div>
</body>
</html>
