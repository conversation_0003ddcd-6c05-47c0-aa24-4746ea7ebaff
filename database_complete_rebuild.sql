-- قاعدة بيانات نظام إدارة المراكز الصحية - النسخة المحدثة والكاملة
-- Healthcare Centers Management System Database - Complete Updated Version
-- تم إنشاؤها بناءً على تحليل جميع ملفات المشروع

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS csdb 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE csdb;

-- ===================================
-- الجداول الأساسية
-- ===================================

-- جدول المراكز الصحية
CREATE TABLE centers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    phone VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المستخدمين (الممرضين والمشرفين)
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    center VARCHAR(200),
    region VARCHAR(200),
    center_id INT,
    role ENUM('admin', 'nurse', 'supervisor') DEFAULT 'nurse',
    accountType VARCHAR(50) DEFAULT 'nurse',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE SET NULL,
    INDEX idx_username (username),
    INDEX idx_center_id (center_id),
    INDEX idx_role (role)
);

-- جدول الأطفال
CREATE TABLE children (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    birth_date DATE NOT NULL,
    gender ENUM('male', 'female'),
    parent_name VARCHAR(255),
    parent_phone VARCHAR(50),
    address TEXT,
    nurse_id VARCHAR(50),
    center_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (nurse_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE SET NULL,
    INDEX idx_children_nurse (nurse_id),
    INDEX idx_children_center (center_id),
    INDEX idx_children_birth_date (birth_date)
);

-- جدول اللقاحات
CREATE TABLE vaccines (
    id VARCHAR(50) PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_fr VARCHAR(255),
    description TEXT,
    age_months INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مخزون اللقاحات
CREATE TABLE vaccine_stock (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vaccine_id VARCHAR(50),
    user_id VARCHAR(50),
    quantity INT DEFAULT 0,
    expiry_date DATE,
    batch_number VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vaccine_id) REFERENCES vaccines(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_vaccine_user (vaccine_id, user_id)
);

-- جدول تلقيحات الأطفال
CREATE TABLE child_vaccinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    child_id VARCHAR(50),
    vaccine_id VARCHAR(50),
    vaccination_date DATE,
    due_date DATE,
    is_completed BOOLEAN DEFAULT FALSE,
    notes TEXT,
    administered_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (child_id) REFERENCES children(id) ON DELETE CASCADE,
    FOREIGN KEY (vaccine_id) REFERENCES vaccines(id) ON DELETE CASCADE,
    FOREIGN KEY (administered_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_vaccinations_child (child_id),
    INDEX idx_vaccinations_vaccine (vaccine_id),
    INDEX idx_vaccinations_date (vaccination_date)
);

-- جدول سجل استخدام اللقاحات
CREATE TABLE vaccine_usage_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vaccine_id VARCHAR(50),
    user_id VARCHAR(50),
    child_id VARCHAR(50),
    quantity_used INT DEFAULT 1,
    usage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (vaccine_id) REFERENCES vaccines(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (child_id) REFERENCES children(id) ON DELETE CASCADE,
    INDEX idx_usage_log_date (usage_date)
);

-- جدول الأدوية
CREATE TABLE medicines (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    unit VARCHAR(50) DEFAULT 'وحدة',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مخزون الأدوية
CREATE TABLE medicine_stock (
    id INT PRIMARY KEY AUTO_INCREMENT,
    medicine_id VARCHAR(50),
    user_id VARCHAR(50),
    quantity INT DEFAULT 0,
    expiry_date DATE,
    batch_number VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (medicine_id) REFERENCES medicines(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_medicine_user (medicine_id, user_id)
);

-- جدول وسائل منع الحمل
CREATE TABLE contraceptives (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مخزون وسائل منع الحمل
CREATE TABLE contraceptive_stock (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contraceptive_id VARCHAR(50),
    user_id VARCHAR(50),
    quantity INT DEFAULT 0,
    expiry_date DATE,
    batch_number VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (contraceptive_id) REFERENCES contraceptives(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contraceptive_user (contraceptive_id, user_id)
);

-- جدول التخطيط الشهري
CREATE TABLE monthly_planning (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50),
    month_number INT,
    month_name VARCHAR(100),
    item_type ENUM('vaccine', 'medicine', 'contraceptive'),
    item_id VARCHAR(50),
    planned_quantity INT DEFAULT 0,
    year INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_planning (user_id, month_number, year, item_type, item_id)
);

-- جدول الرسائل
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id VARCHAR(50),
    receiver_id VARCHAR(50),
    message TEXT NOT NULL,
    attachments JSON,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_messages_sender (sender_id),
    INDEX idx_messages_receiver (receiver_id),
    INDEX idx_messages_sent_at (sent_at)
);

-- جدول المهام
CREATE TABLE tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    due_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_tasks_user (user_id),
    INDEX idx_tasks_status (status)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50),
    title VARCHAR(255) NOT NULL,
    message TEXT,
    type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_notifications_user (user_id),
    INDEX idx_notifications_read (is_read)
);

-- جدول الإحصائيات الشهرية
CREATE TABLE monthly_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50),
    month_key VARCHAR(20),
    total_children INT DEFAULT 0,
    total_vaccinations INT DEFAULT 0,
    completion_rate DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_month (user_id, month_key)
);

-- جدول إعدادات المستخدم
CREATE TABLE user_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50),
    setting_key VARCHAR(100),
    setting_value JSON,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_setting (user_id, setting_key)
);

-- ===================================
-- الجداول الإضافية المكتشفة من API
-- ===================================

-- جدول قائمة اللقاحات المخصصة للمستخدمين
CREATE TABLE vaccine_list (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    center_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    quantity INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_center_id (center_id),
    INDEX idx_vaccine_list_name (name)
);

-- جدول قائمة الأدوية المخصصة للمستخدمين
CREATE TABLE medicine_list (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    center_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    unit VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_center_id (center_id),
    INDEX idx_medicine_list_name (name)
);

-- جدول التخطيط الشهري للقاحات
CREATE TABLE vaccine_monthly_planning (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL,
    center_id VARCHAR(50) NOT NULL,
    month_key VARCHAR(20) NOT NULL,
    month_name VARCHAR(100),
    vaccines_data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_month (user_id, month_key),
    INDEX idx_user_id (user_id),
    INDEX idx_center_id (center_id),
    INDEX idx_vaccine_planning_month (month_key)
);

-- جدول قائمة وسائل منع الحمل المخصصة
CREATE TABLE contraceptive_list (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    center_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_center_id (center_id),
    INDEX idx_contraceptive_list_name (name)
);

-- جدول التخطيط الشهري لتنظيم الأسرة
CREATE TABLE family_planning_monthly_planning (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL,
    center_id VARCHAR(50) NOT NULL,
    month_key VARCHAR(20) NOT NULL,
    month_name VARCHAR(100),
    contraceptives_data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_month (user_id, month_key),
    INDEX idx_user_id (user_id),
    INDEX idx_center_id (center_id),
    INDEX idx_family_planning_month (month_key)
);

-- ===================================
-- البيانات الافتراضية
-- ===================================

-- إدراج بيانات افتراضية للمراكز الصحية
INSERT INTO centers (name, location, phone) VALUES
('المركز الصحي الرئيسي', 'المدينة المركزية', '123456789'),
('مركز صحة الأطفال', 'الحي الشمالي', '987654321'),
('مركز الرعاية الأولية', 'الحي الجنوبي', '456789123');

-- إدراج بيانات افتراضية للقاحات
INSERT INTO vaccines (id, name_ar, name_fr, age_months) VALUES
('bcg', 'لقاح السل', 'BCG', 0),
('hepatitis_b_birth', 'التهاب الكبد ب (عند الولادة)', 'Hépatite B (naissance)', 0),
('polio_0', 'شلل الأطفال 0', 'Polio 0', 0),
('dtp_hib_hep_b_1', 'الثلاثي + المستدمية النزلية + التهاب الكبد ب 1', 'DTC-Hib-HepB 1', 2),
('polio_1', 'شلل الأطفال 1', 'Polio 1', 2),
('pneumococcal_1', 'المكورات الرئوية 1', 'Pneumocoque 1', 2),
('rotavirus_1', 'فيروس الروتا 1', 'Rotavirus 1', 2),
('dtp_hib_hep_b_2', 'الثلاثي + المستدمية النزلية + التهاب الكبد ب 2', 'DTC-Hib-HepB 2', 4),
('polio_2', 'شلل الأطفال 2', 'Polio 2', 4),
('pneumococcal_2', 'المكورات الرئوية 2', 'Pneumocoque 2', 4),
('rotavirus_2', 'فيروس الروتا 2', 'Rotavirus 2', 4),
('dtp_hib_hep_b_3', 'الثلاثي + المستدمية النزلية + التهاب الكبد ب 3', 'DTC-Hib-HepB 3', 6),
('polio_3', 'شلل الأطفال 3', 'Polio 3', 6),
('pneumococcal_3', 'المكورات الرئوية 3', 'Pneumocoque 3', 6),
('rotavirus_3', 'فيروس الروتا 3', 'Rotavirus 3', 6),
('measles_rubella_1', 'الحصبة والحصبة الألمانية 1', 'RR 1', 9),
('meningococcal', 'المكورات السحائية', 'Méningocoque', 9),
('measles_rubella_2', 'الحصبة والحصبة الألمانية 2', 'RR 2', 18),
('dtp_4', 'الثلاثي 4', 'DTC 4', 18),
('polio_4', 'شلل الأطفال 4', 'Polio 4', 18);

-- إدراج بيانات افتراضية للأدوية
INSERT INTO medicines (id, name, unit) VALUES
('paracetamol_syrup', 'شراب الباراسيتامول', 'زجاجة'),
('amoxicillin_syrup', 'شراب الأموكسيسيلين', 'زجاجة'),
('iron_syrup', 'شراب الحديد', 'زجاجة'),
('vitamin_d_drops', 'قطرات فيتامين د', 'زجاجة'),
('oral_rehydration_salts', 'أملاح الإماهة الفموية', 'كيس'),
('zinc_tablets', 'أقراص الزنك', 'قرص'),
('albendazole_tablets', 'أقراص الألبيندازول', 'قرص'),
('mebendazole_tablets', 'أقراص الميبيندازول', 'قرص');

-- إدراج بيانات افتراضية لوسائل منع الحمل
INSERT INTO contraceptives (id, name, type) VALUES
('combined_pill', 'حبوب منع الحمل المركبة', 'هرموني'),
('progestin_pill', 'حبوب البروجستين', 'هرموني'),
('iud_copper', 'اللولب النحاسي', 'جهاز داخل الرحم'),
('iud_hormonal', 'اللولب الهرموني', 'جهاز داخل الرحم'),
('injection_3month', 'حقنة 3 أشهر', 'هرموني'),
('implant', 'الغرسة', 'هرموني'),
('condoms_male', 'الواقي الذكري', 'حاجز'),
('condoms_female', 'الواقي الأنثوي', 'حاجز'),
('diaphragm', 'الحجاب الحاجز', 'حاجز'),
('emergency_pill', 'حبة الطوارئ', 'هرموني');

-- إنشاء مستخدمين افتراضيين
INSERT INTO users (id, username, password, name, center_id, role) VALUES
('admin_001', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 1, 'admin'),
('nurse_001', 'nurse1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'الممرضة فاطمة أحمد', 1, 'nurse'),
('nurse_002', 'nurse2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'الممرضة عائشة محمد', 2, 'nurse'),
('supervisor_001', 'supervisor1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المشرفة خديجة علي', 1, 'supervisor');

-- ===================================
-- إنشاء Views مفيدة
-- ===================================

-- عرض الأطفال مع بيانات الممرض والمركز
CREATE VIEW children_with_nurse AS
SELECT
    c.*,
    u.name as nurse_name,
    ct.name as center_name
FROM children c
LEFT JOIN users u ON c.nurse_id = u.id
LEFT JOIN centers ct ON c.center_id = ct.id;

-- عرض جدول التلقيحات
CREATE VIEW vaccination_schedule AS
SELECT
    c.id as child_id,
    c.name as child_name,
    c.birth_date,
    v.id as vaccine_id,
    v.name_ar as vaccine_name,
    v.age_months,
    DATE_ADD(c.birth_date, INTERVAL v.age_months MONTH) as due_date,
    cv.is_completed,
    cv.vaccination_date
FROM children c
CROSS JOIN vaccines v
LEFT JOIN child_vaccinations cv ON c.id = cv.child_id AND v.id = cv.vaccine_id
WHERE v.is_active = TRUE
ORDER BY c.id, v.age_months;

-- عرض ملخص المخزون
CREATE VIEW stock_summary AS
SELECT
    'vaccine' as item_type,
    v.name_ar as item_name,
    vs.user_id,
    u.name as user_name,
    SUM(vs.quantity) as total_quantity,
    COUNT(vs.id) as stock_entries
FROM vaccine_stock vs
JOIN vaccines v ON vs.vaccine_id = v.id
JOIN users u ON vs.user_id = u.id
GROUP BY v.id, vs.user_id

UNION ALL

SELECT
    'medicine' as item_type,
    m.name as item_name,
    ms.user_id,
    u.name as user_name,
    SUM(ms.quantity) as total_quantity,
    COUNT(ms.id) as stock_entries
FROM medicine_stock ms
JOIN medicines m ON ms.medicine_id = m.id
JOIN users u ON ms.user_id = u.id
GROUP BY m.id, ms.user_id

UNION ALL

SELECT
    'contraceptive' as item_type,
    c.name as item_name,
    cs.user_id,
    u.name as user_name,
    SUM(cs.quantity) as total_quantity,
    COUNT(cs.id) as stock_entries
FROM contraceptive_stock cs
JOIN contraceptives c ON cs.contraceptive_id = c.id
JOIN users u ON cs.user_id = u.id
GROUP BY c.id, cs.user_id;

-- ===================================
-- تعليقات ومعلومات إضافية
-- ===================================

/*
ملاحظات مهمة:
1. تم إنشاء هذا الملف بناءً على تحليل شامل لجميع ملفات المشروع
2. يحتوي على جميع الجداول المكتشفة في ملفات API
3. تم إضافة الفهارس المناسبة لتحسين الأداء
4. تم إدراج البيانات الافتراضية الأساسية
5. اسم قاعدة البيانات: csdb (كما هو مستخدم في البيئة الحالية)
6. الترميز: utf8mb4_unicode_ci لدعم النصوص العربية بشكل كامل

الجداول الرئيسية:
- centers: المراكز الصحية
- users: المستخدمين (ممرضين، مشرفين، مدراء)
- children: بيانات الأطفال
- vaccines: قائمة اللقاحات
- vaccine_stock: مخزون اللقاحات
- child_vaccinations: سجل تلقيحات الأطفال
- medicines: قائمة الأدوية
- medicine_stock: مخزون الأدوية
- contraceptives: وسائل منع الحمل
- contraceptive_stock: مخزون وسائل منع الحمل
- messages: نظام الرسائل
- tasks: إدارة المهام
- notifications: الإشعارات
- monthly_planning: التخطيط الشهري

الجداول الإضافية المكتشفة:
- vaccine_list: قوائم اللقاحات المخصصة للمستخدمين
- medicine_list: قوائم الأدوية المخصصة للمستخدمين
- contraceptive_list: قوائم وسائل منع الحمل المخصصة
- vaccine_monthly_planning: التخطيط الشهري للقاحات
- family_planning_monthly_planning: التخطيط الشهري لتنظيم الأسرة
*/
