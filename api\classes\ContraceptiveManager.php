<?php
/**
 * فئة إدارة وسائل منع الحمل
 * Contraceptive Management Class
 */

class ContraceptiveManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على جميع وسائل منع الحمل
     * Get all contraceptives
     */
    public function getAllContraceptives() {
        try {
            $sql = "SELECT * FROM contraceptives WHERE is_active = 1 ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get all contraceptives error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مخزون وسائل منع الحمل للمستخدم
     * Get contraceptive stock for user
     */
    public function getContraceptiveStock($user_id) {
        try {
            $sql = "SELECT cs.*, c.name, c.type 
                    FROM contraceptive_stock cs
                    JOIN contraceptives c ON cs.contraceptive_id = c.id
                    WHERE cs.user_id = ?
                    ORDER BY c.name";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get contraceptive stock error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث مخزون وسيلة منع الحمل
     * Update contraceptive stock
     */
    public function updateContraceptiveStock($user_id, $contraceptive_id, $quantity, $expiry_date = null, $batch_number = null) {
        try {
            $sql = "INSERT INTO contraceptive_stock (contraceptive_id, user_id, quantity, expiry_date, batch_number)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    quantity = VALUES(quantity),
                    expiry_date = VALUES(expiry_date),
                    batch_number = VALUES(batch_number),
                    updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$contraceptive_id, $user_id, $quantity, $expiry_date, $batch_number]);
            
        } catch (Exception $e) {
            error_log('Update contraceptive stock error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إضافة وسيلة منع حمل جديدة
     * Add new contraceptive
     */
    public function addContraceptive($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required_fields = ['id', 'name'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("الحقل {$field} مطلوب");
                }
            }
            
            // التحقق من عدم تكرار المعرف
            if ($this->contraceptiveExists($data['id'])) {
                throw new Exception('معرف وسيلة منع الحمل موجود مسبقاً');
            }
            
            $sql = "INSERT INTO contraceptives (id, name, type, description, is_active)
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                $data['id'],
                $data['name'],
                $data['type'] ?? null,
                $data['description'] ?? null,
                $data['is_active'] ?? 1
            ]);
            
        } catch (Exception $e) {
            error_log('Add contraceptive error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث وسيلة منع الحمل
     * Update contraceptive
     */
    public function updateContraceptive($id, $data) {
        try {
            // التحقق من وجود وسيلة منع الحمل
            if (!$this->contraceptiveExists($id)) {
                throw new Exception('وسيلة منع الحمل غير موجودة');
            }
            
            // بناء استعلام التحديث
            $update_fields = [];
            $params = [];
            
            $allowed_fields = ['name', 'type', 'description', 'is_active'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = ?";
                    $params[] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                throw new Exception('لا توجد بيانات للتحديث');
            }
            
            $params[] = $id;
            
            $sql = "UPDATE contraceptives SET " . implode(', ', $update_fields) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
            
        } catch (Exception $e) {
            error_log('Update contraceptive error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف وسيلة منع الحمل
     * Delete contraceptive
     */
    public function deleteContraceptive($id) {
        try {
            // التحقق من وجود وسيلة منع الحمل
            if (!$this->contraceptiveExists($id)) {
                throw new Exception('وسيلة منع الحمل غير موجودة');
            }
            
            // حذف وسيلة منع الحمل (سيتم حذف المخزون تلقائياً بسبب CASCADE)
            $sql = "DELETE FROM contraceptives WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$id]);
            
        } catch (Exception $e) {
            error_log('Delete contraceptive error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * التحقق من وجود وسيلة منع الحمل
     * Check if contraceptive exists
     */
    private function contraceptiveExists($id) {
        try {
            $sql = "SELECT COUNT(*) FROM contraceptives WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log('Contraceptive exists check error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات وسائل منع الحمل
     * Get contraceptive statistics
     */
    public function getContraceptiveStats($user_id = null, $center_id = null) {
        try {
            $stats = [];
            
            // إجمالي وسائل منع الحمل المتاحة
            $sql = "SELECT COUNT(*) FROM contraceptives WHERE is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['total_contraceptives'] = $stmt->fetchColumn();
            
            // إجمالي المخزون
            $sql = "SELECT SUM(quantity) FROM contraceptive_stock";
            $params = [];
            
            if ($user_id) {
                $sql .= " WHERE user_id = ?";
                $params[] = $user_id;
            } elseif ($center_id) {
                $sql .= " WHERE user_id IN (SELECT id FROM users WHERE center_id = ?)";
                $params[] = $center_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['total_stock'] = $stmt->fetchColumn() ?: 0;
            
            // وسائل منع الحمل منتهية الصلاحية
            $sql = "SELECT COUNT(*) FROM contraceptive_stock 
                    WHERE expiry_date IS NOT NULL AND expiry_date < CURRENT_DATE()";
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params = [$user_id];
            } elseif ($center_id) {
                $sql .= " AND user_id IN (SELECT id FROM users WHERE center_id = ?)";
                $params = [$center_id];
            } else {
                $params = [];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $stats['expired_contraceptives'] = $stmt->fetchColumn() ?: 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Get contraceptive stats error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على التخطيط الشهري لوسائل منع الحمل
     * Get monthly contraceptive planning
     */
    public function getMonthlyPlanning($user_id, $year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }
            
            $sql = "SELECT mp.*, c.name, c.type
                    FROM monthly_planning mp
                    JOIN contraceptives c ON mp.item_id = c.id
                    WHERE mp.user_id = ? AND mp.item_type = 'contraceptive' AND mp.year = ?
                    ORDER BY mp.month_number, c.name";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $year]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('Get monthly planning error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث التخطيط الشهري
     * Update monthly planning
     */
    public function updateMonthlyPlanning($user_id, $month_number, $month_name, $contraceptive_id, $planned_quantity, $year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }
            
            $sql = "INSERT INTO monthly_planning (user_id, month_number, month_name, item_type, item_id, planned_quantity, year)
                    VALUES (?, ?, ?, 'contraceptive', ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    month_name = VALUES(month_name),
                    planned_quantity = VALUES(planned_quantity),
                    updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id, $month_number, $month_name, $contraceptive_id, $planned_quantity, $year]);
            
        } catch (Exception $e) {
            error_log('Update monthly planning error: ' . $e->getMessage());
            return false;
        }
    }
}
?>
